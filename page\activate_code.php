<?php
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $activation_code = trim($_POST['activation_code']);
    
    if (empty($activation_code)) {
        $error = 'يرجى إدخال كود التفعيل';
    } else {
        try {
            $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Check if code exists and is valid
            $stmt = $db->prepare("SELECT ac.*, sp.name as plan_name, sp.duration_days, sp.icon, sp.color
                                 FROM activation_codes ac 
                                 JOIN subscription_plans sp ON ac.plan_id = sp.id
                                 WHERE ac.code = ? AND ac.is_used = 0 AND sp.is_active = 1
                                 AND (ac.expires_at IS NULL OR ac.expires_at > NOW())");
            $stmt->execute([$activation_code]);
            $code_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$code_data) {
                $error = 'كود التفعيل غير صحيح أو منتهي الصلاحية';
            } else {
                // Start transaction
                $db->beginTransaction();
                
                try {
                    // Mark code as used
                    $stmt = $db->prepare("UPDATE activation_codes SET is_used = 1, used_by = ?, used_at = NOW() WHERE id = ?");
                    $stmt->execute([$user_id, $code_data['id']]);
                    
                    // Calculate end date
                    $end_date = date('Y-m-d H:i:s', strtotime('+' . $code_data['duration_days'] . ' days'));
                    
                    // Create subscription
                    $stmt = $db->prepare("INSERT INTO user_subscriptions (user_id, plan_id, activation_code, payment_method, payment_status, amount_paid, start_date, end_date) VALUES (?, ?, ?, 'code', 'completed', 0, NOW(), ?)");
                    $stmt->execute([$user_id, $code_data['plan_id'], $activation_code, $end_date]);
                    
                    // Update user subscription status
                    $stmt = $db->prepare("UPDATE users SET subscription_status = 'active', current_plan_id = ?, subscription_end_date = ? WHERE id = ?");
                    $stmt->execute([$code_data['plan_id'], $end_date, $user_id]);
                    
                    // Commit transaction
                    $db->commit();
                    
                    // Redirect to success page with celebration
                    header('Location: subscription_success.php?plan_name=' . urlencode($code_data['plan_name']) . '&end_date=' . urlencode($end_date) . '&method=code');
                    exit;
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'حدث خطأ أثناء تفعيل الكود. يرجى المحاولة مرة أخرى.';
                }
            }
            
        } catch (Exception $e) {
            $error = 'حدث خطأ في الاتصال بقاعدة البيانات';
        }
    }
}

// Get plan details if coming from payment methods
$plan_id = isset($_GET['plan_id']) ? intval($_GET['plan_id']) : 0;
$plan_name = isset($_GET['plan_name']) ? $_GET['plan_name'] : '';
$price = isset($_GET['price']) ? floatval($_GET['price']) : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفعيل بكود السنتر - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="page-container">
        <div class="page-header">
            <h1>تفعيل بكود السنتر</h1>
            <p>أدخل كود التفعيل الذي حصلت عليه من معلمك</p>
        </div>

        <div class="activation-container">
            <div class="activation-card">
                <div class="card-header">
                    <div class="activation-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h2>تفعيل الاشتراك</h2>
                    <p>أدخل كود التفعيل للحصول على الوصول الكامل للمحتوى التعليمي</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" class="activation-form">
                    <div class="form-group">
                        <label for="activation_code">كود التفعيل</label>
                        <input type="text" 
                               id="activation_code" 
                               name="activation_code" 
                               required 
                               placeholder="أدخل كود التفعيل"
                               maxlength="50"
                               style="text-transform: uppercase;"
                               autocomplete="off">
                        <div class="input-help">
                            <i class="fas fa-info-circle"></i>
                            الكود يتكون من أحرف وأرقام باللغة الإنجليزية
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i>
                            تفعيل الكود
                        </button>
                    </div>
                </form>

                <div class="help-section">
                    <h3>تحتاج مساعدة؟</h3>
                    <div class="help-items">
                        <div class="help-item">
                            <i class="fas fa-user-graduate"></i>
                            <div>
                                <h4>احصل على الكود من معلمك</h4>
                                <p>إذا كنت طالباً في أحد السناتر، اطلب كود التفعيل من معلمك</p>
                            </div>
                        </div>
                        
                        <div class="help-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <h4>تواصل معنا</h4>
                                <p>للاستفسارات: <strong>01126130559</strong></p>
                            </div>
                        </div>
                        
                        <div class="help-item">
                            <i class="fas fa-shopping-cart"></i>
                            <div>
                                <h4>اشترك مباشرة</h4>
                                <p><a href="subscriptions.php">اختر خطة اشتراك مدفوعة</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-actions">
            <a href="<?php echo $plan_id ? 'payment_methods.php?plan_id=' . $plan_id . '&plan_name=' . urlencode($plan_name) . '&price=' . $price : 'subscriptions.php'; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                <?php echo $plan_id ? 'العودة لطرق الدفع' : 'العودة للخطط'; ?>
            </a>
        </div>
    </div>

    <script>
        // Auto uppercase the input
        document.getElementById('activation_code').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Add loading state to form
        document.querySelector('.activation-form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التفعيل...';
            
            // Re-enable after 10 seconds in case of error
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }, 10000);
        });
    </script>

    <style>
        .page-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, 
                rgba(135, 206, 235, 0.02) 0%, 
                rgba(70, 130, 180, 0.05) 50%, 
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 0;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.9) 0%, 
                rgba(248, 249, 250, 0.8) 100%);
            border-radius: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .page-header h1 {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .activation-container {
            margin-bottom: 30px;
        }

        .activation-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-top: 5px solid #17a2b8;
        }

        .card-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .activation-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #17a2b8, #138496);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px auto;
            color: white;
            font-size: 2rem;
        }

        .card-header h2 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.8rem;
        }

        .card-header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .activation-form {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 18px;
            font-family: monospace;
            text-align: center;
            letter-spacing: 2px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            border-color: #17a2b8;
            box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.1);
            outline: none;
        }

        .input-help {
            margin-top: 8px;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-actions {
            text-align: center;
        }

        .help-section {
            border-top: 1px solid #eee;
            padding-top: 30px;
        }

        .help-section h3 {
            color: #333;
            margin: 0 0 20px 0;
            text-align: center;
            font-size: 1.3rem;
        }

        .help-items {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .help-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .help-item i {
            font-size: 1.5rem;
            color: #17a2b8;
            margin-top: 5px;
        }

        .help-item h4 {
            color: #333;
            margin: 0 0 5px 0;
            font-size: 16px;
        }

        .help-item p {
            color: #666;
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
        }

        .help-item a {
            color: #17a2b8;
            text-decoration: none;
            font-weight: bold;
        }

        .help-item a:hover {
            text-decoration: underline;
        }

        .page-actions {
            text-align: center;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            min-width: 200px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .activation-card {
                padding: 25px;
            }
            
            .help-items {
                gap: 15px;
            }
            
            .help-item {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>


    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
