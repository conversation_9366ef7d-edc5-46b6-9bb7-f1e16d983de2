<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/NewsManager.php';
require_once __DIR__ . '/../includes/SecurityHelper.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$newsManager = new NewsManager();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = SecurityHelper::sanitizeInput([
                    'title' => $_POST['title'] ?? '',
                    'content' => $_POST['content'] ?? '',
                    'summary' => $_POST['summary'] ?? '',
                    'category' => $_POST['category'] ?? 'general',
                    'education_level' => $_POST['education_level'] ?? 'all',
                    'education_type' => $_POST['education_type'] ?? 'all',
                    'grade' => $_POST['grade'] ?? 'all',
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : 'all',
                    'publication_date' => $_POST['publication_date'] ?? date('Y-m-d'),
                    'is_published' => isset($_POST['is_published']) ? 1 : 0,
                    'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                    'created_by' => $_SESSION['admin_id']
                ]);

                $validationErrors = SecurityHelper::validateNewsData($data);
                if (!empty($validationErrors)) {
                    $message = implode('<br>', $validationErrors);
                    $messageType = 'error';
                } elseif ($newsManager->addNews($data)) {
                    $message = 'تم إضافة الخبر بنجاح';
                    $messageType = 'success';
                    // SecurityHelper::logAdminActivity('add_news', ['title' => $data['title']]);
                } else {
                    $message = 'حدث خطأ أثناء إضافة الخبر';
                    $messageType = 'error';
                }
                break;
                
            case 'edit':
                $data = [
                    'title' => $_POST['title'],
                    'content' => $_POST['content'],
                    'summary' => $_POST['summary'],
                    'category' => $_POST['category'],
                    'education_level' => $_POST['education_level'],
                    'education_type' => $_POST['education_type'],
                    'grade' => $_POST['grade'],
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : 'all',
                    'publication_date' => $_POST['publication_date'],
                    'is_published' => isset($_POST['is_published']) ? 1 : 0,
                    'is_featured' => isset($_POST['is_featured']) ? 1 : 0
                ];
                
                if ($newsManager->updateNews($_POST['news_id'], $data)) {
                    $message = 'تم تحديث الخبر بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث الخبر';
                    $messageType = 'error';
                }
                break;
                
            case 'delete':
                if ($newsManager->deleteNews($_POST['news_id'])) {
                    $message = 'تم حذف الخبر بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف الخبر';
                    $messageType = 'error';
                }
                break;
                
            case 'toggle_status':
                if ($newsManager->toggleNewsStatus($_POST['news_id'])) {
                    $message = 'تم تغيير حالة النشر بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تغيير حالة النشر';
                    $messageType = 'error';
                }
                break;
                
            case 'toggle_featured':
                if ($newsManager->toggleFeaturedStatus($_POST['news_id'])) {
                    $message = 'تم تغيير حالة الإبراز بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تغيير حالة الإبراز';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get filters
$filters = [];
if (!empty($_GET['category'])) $filters['category'] = $_GET['category'];
if (!empty($_GET['education_level'])) $filters['education_level'] = $_GET['education_level'];
if (!empty($_GET['education_type'])) $filters['education_type'] = $_GET['education_type'];
if (!empty($_GET['grade'])) $filters['grade'] = $_GET['grade'];
if (isset($_GET['is_published']) && $_GET['is_published'] !== '') $filters['is_published'] = $_GET['is_published'];
if (isset($_GET['is_featured']) && $_GET['is_featured'] !== '') $filters['is_featured'] = $_GET['is_featured'];

$news = $newsManager->getAllNews($filters);
$totalNews = $newsManager->getTotalNewsCount();
$categories = $newsManager->getCategories();

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أخبار المنهج - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <style>
        .news-form {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .news-form h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(70, 130, 180, 0.2);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #87CEEB;
            box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
        }
        
        .checkbox-group {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .news-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .filters-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-toggle {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-feature {
            background: #17a2b8;
            color: white;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-published {
            background: #d4edda;
            color: #155724;
        }
        
        .status-draft {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-featured {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .news-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .news-form {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .checkbox-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
         <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Add News Form -->
                <div class="news-form">
                    <h3>إضافة خبر جديد</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="add">
                        <input type="hidden" name="csrf_token" value="<?php echo SecurityHelper::generateCSRFToken(); ?>">
                        
                        <div class="form-group">
                            <label for="title">عنوان الخبر *</label>
                            <input type="text" id="title" name="title" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="summary">ملخص الخبر</label>
                            <textarea id="summary" name="summary" rows="3" placeholder="ملخص مختصر للخبر..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="content">محتوى الخبر *</label>
                            <textarea id="content" name="content" rows="8" required placeholder="اكتب محتوى الخبر هنا..."></textarea>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="category">التصنيف</label>
                                <input type="text" id="category" name="category" value="general" placeholder="مثال: منهج، امتحانات، إعلانات">
                            </div>
                            
                            <div class="form-group">
                                <label for="publication_date">تاريخ النشر *</label>
                                <input type="date" id="publication_date" name="publication_date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="education_level">المرحلة التعليمية</label>
                                <select id="education_level" name="education_level">
                                    <option value="all">جميع المراحل</option>
                                    <option value="primary">ابتدائي</option>
                                    <option value="preparatory">إعدادي</option>
                                    <option value="secondary">ثانوي</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="education_type">نوع التعليم</label>
                                <select id="education_type" name="education_type">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="general">عام</option>
                                    <option value="azhari">أزهري</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="grade">الصف</label>
                                <select id="grade" name="grade">
                                    <option value="all">جميع الصفوف</option>
                                </select>
                            </div>

                            <div id="specialization_container" class="form-group" style="display: none;">
                                <label for="specialization">التخصص</label>
                                <select id="specialization" name="specialization">
                                    <option value="all">جميع التخصصات</option>
                                    <option value="scientific">علمي</option>
                                    <option value="literary">أدبي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>خيارات النشر</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="is_published" name="is_published" checked>
                                    <label for="is_published">نشر الخبر</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="is_featured" name="is_featured">
                                    <label for="is_featured">خبر مميز</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn-primary">إضافة الخبر</button>
                    </form>
                </div>

                <!-- News List -->
                <div class="news-table">
                    <div class="table-header">
                        قائمة الأخبار (<?php echo count($news); ?> خبر)
                    </div>

                    <!-- Filters -->
                    <div class="filters-section">
                        <form method="GET">
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label for="filter_category">التصنيف</label>
                                    <select id="filter_category" name="category">
                                        <option value="">جميع التصنيفات</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category); ?>" <?php echo ($_GET['category'] ?? '') === $category ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_education_level">المرحلة التعليمية</label>
                                    <select id="filter_education_level" name="education_level">
                                        <option value="">جميع المراحل</option>
                                        <option value="all" <?php echo ($_GET['education_level'] ?? '') === 'all' ? 'selected' : ''; ?>>جميع المراحل</option>
                                        <option value="primary" <?php echo ($_GET['education_level'] ?? '') === 'primary' ? 'selected' : ''; ?>>ابتدائي</option>
                                        <option value="preparatory" <?php echo ($_GET['education_level'] ?? '') === 'preparatory' ? 'selected' : ''; ?>>إعدادي</option>
                                        <option value="secondary" <?php echo ($_GET['education_level'] ?? '') === 'secondary' ? 'selected' : ''; ?>>ثانوي</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_education_type">نوع التعليم</label>
                                    <select id="filter_education_type" name="education_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="all" <?php echo ($_GET['education_type'] ?? '') === 'all' ? 'selected' : ''; ?>>جميع الأنواع</option>
                                        <option value="general" <?php echo ($_GET['education_type'] ?? '') === 'general' ? 'selected' : ''; ?>>عام</option>
                                        <option value="azhari" <?php echo ($_GET['education_type'] ?? '') === 'azhari' ? 'selected' : ''; ?>>أزهري</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_is_published">حالة النشر</label>
                                    <select id="filter_is_published" name="is_published">
                                        <option value="">جميع الحالات</option>
                                        <option value="1" <?php echo ($_GET['is_published'] ?? '') === '1' ? 'selected' : ''; ?>>منشور</option>
                                        <option value="0" <?php echo ($_GET['is_published'] ?? '') === '0' ? 'selected' : ''; ?>>مسودة</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_is_featured">الأخبار المميزة</label>
                                    <select id="filter_is_featured" name="is_featured">
                                        <option value="">جميع الأخبار</option>
                                        <option value="1" <?php echo ($_GET['is_featured'] ?? '') === '1' ? 'selected' : ''; ?>>مميز</option>
                                        <option value="0" <?php echo ($_GET['is_featured'] ?? '') === '0' ? 'selected' : ''; ?>>عادي</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn-primary">تصفية</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>التصنيف</th>
                                    <th>المحتوى</th>
                                    <th>المرحلة/النوع</th>
                                    <th>تاريخ النشر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($news)): ?>
                                    <tr>
                                        <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                                            لا توجد أخبار مطابقة للمعايير المحددة
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($news as $newsItem): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($newsItem['title']); ?></strong>
                                                <?php if ($newsItem['is_featured']): ?>
                                                    <span class="status-badge status-featured">مميز</span>
                                                <?php endif; ?>
                                                <?php if ($newsItem['summary']): ?>
                                                    <br><small style="color: #6c757d;"><?php echo htmlspecialchars(substr($newsItem['summary'], 0, 50)) . (strlen($newsItem['summary']) > 50 ? '...' : ''); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($newsItem['category']); ?></td>
                                            <td>
                                                <div class="news-content">
                                                    <?php echo htmlspecialchars(substr(strip_tags($newsItem['content']), 0, 100)) . (strlen($newsItem['content']) > 100 ? '...' : ''); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $educationLevels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي', 'all' => 'الجميع'];
                                                $educationTypes = ['general' => 'عام', 'azhari' => 'أزهري', 'all' => 'الجميع'];
                                                echo $educationLevels[$newsItem['education_level']] . ' / ' . $educationTypes[$newsItem['education_type']];
                                                if ($newsItem['grade'] !== 'all') {
                                                    echo ' / ' . $newsItem['grade'];
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo date('Y/m/d', strtotime($newsItem['publication_date'])); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $newsItem['is_published'] ? 'status-published' : 'status-draft'; ?>">
                                                    <?php echo $newsItem['is_published'] ? 'منشور' : 'مسودة'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-sm btn-edit" onclick="editNews(<?php echo $newsItem['id']; ?>)">تعديل</button>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تغيير حالة النشر؟')">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="news_id" value="<?php echo $newsItem['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-toggle">
                                                            <?php echo $newsItem['is_published'] ? 'إلغاء نشر' : 'نشر'; ?>
                                                        </button>
                                                    </form>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تغيير حالة الإبراز؟')">
                                                        <input type="hidden" name="action" value="toggle_featured">
                                                        <input type="hidden" name="news_id" value="<?php echo $newsItem['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-feature">
                                                            <?php echo $newsItem['is_featured'] ? 'إلغاء إبراز' : 'إبراز'; ?>
                                                        </button>
                                                    </form>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الخبر؟')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="news_id" value="<?php echo $newsItem['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-delete">حذف</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit News Modal -->
    <div id="editNewsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل الخبر</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editNewsForm" method="POST">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="news_id" id="edit_news_id">

                    <div class="form-group">
                        <label for="edit_title">عنوان الخبر *</label>
                        <input type="text" id="edit_title" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_summary">ملخص الخبر</label>
                        <textarea id="edit_summary" name="summary" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="edit_content">محتوى الخبر *</label>
                        <textarea id="edit_content" name="content" rows="8" required></textarea>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_category">التصنيف</label>
                            <input type="text" id="edit_category" name="category">
                        </div>

                        <div class="form-group">
                            <label for="edit_publication_date">تاريخ النشر *</label>
                            <input type="date" id="edit_publication_date" name="publication_date" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_education_level">المرحلة التعليمية</label>
                            <select id="edit_education_level" name="education_level">
                                <option value="all">جميع المراحل</option>
                                <option value="primary">ابتدائي</option>
                                <option value="preparatory">إعدادي</option>
                                <option value="secondary">ثانوي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_education_type">نوع التعليم</label>
                            <select id="edit_education_type" name="education_type">
                                <option value="all">جميع الأنواع</option>
                                <option value="general">عام</option>
                                <option value="azhari">أزهري</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_grade">الصف</label>
                            <select id="edit_grade" name="grade">
                                <option value="all">جميع الصفوف</option>
                            </select>
                        </div>

                        <div id="edit_specialization_container" class="form-group" style="display: none;">
                            <label for="edit_specialization">التخصص</label>
                            <select id="edit_specialization" name="specialization">
                                <option value="all">جميع التخصصات</option>
                                <option value="scientific">علمي</option>
                                <option value="literary">أدبي</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>خيارات النشر</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="edit_is_published" name="is_published">
                                <label for="edit_is_published">نشر الخبر</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="edit_is_featured" name="is_featured">
                                <label for="edit_is_featured">خبر مميز</label>
                            </div>
                        </div>
                    </div>

                    <div class="modal-buttons">
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                        <button type="button" class="btn-secondary" onclick="closeEditModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="<?php echo SITE_URL; ?>/admin/js/education-selector.js"></script>
    <script>
        function editNews(newsId) {
            // Fetch news data via AJAX
            fetch(`get_news.php?id=${newsId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const news = data.news;
                        document.getElementById('edit_news_id').value = news.id;
                        document.getElementById('edit_title').value = news.title;
                        document.getElementById('edit_summary').value = news.summary || '';
                        document.getElementById('edit_content').value = news.content;
                        document.getElementById('edit_category').value = news.category;
                        document.getElementById('edit_publication_date').value = news.publication_date;
                        document.getElementById('edit_is_published').checked = news.is_published == 1;
                        document.getElementById('edit_is_featured').checked = news.is_featured == 1;

                        // Set education values using the education selector
                        EducationSelector.setValues('edit_', news.education_level, news.education_type, news.grade, news.specialization);

                        document.getElementById('editNewsModal').style.display = 'block';
                    } else {
                        alert('حدث خطأ في تحميل بيانات الخبر');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تحميل بيانات الخبر');
                });
        }

        function closeEditModal() {
            document.getElementById('editNewsModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editNewsModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 900px;
            max-height: 95vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
    <script src="js/admin-modern.js"></script>
</body>
</html>
