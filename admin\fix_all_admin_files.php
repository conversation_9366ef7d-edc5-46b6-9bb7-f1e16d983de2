<?php
/**
 * <PERSON><PERSON><PERSON> to fix all admin files with modern design and modals
 */

// Files to update
$files = [
    'courses.php',
    'news.php', 
    'exams.php',
    'manage_notifications.php',
    'send_notifications.php',
    'payment_requests.php',
    'student_notes.php',
    'add_admin.php',
    'manage_questions.php',
    'honor_board.php',
    'curriculum_subjects.php',
    'course_content.php',
    'course_subscribers.php',
    'subscribers.php',
    'extend_subscription.php',
    'messages.php',
    'reset_admin.php',
    'test_notifications_system.php',
    'test_session.php',
    'create_test_admin.php'
];

foreach ($files as $file) {
    $filePath = __DIR__ . '/' . $file;
    
    if (!file_exists($filePath)) {
        echo "File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix CSS paths
    $content = preg_replace('/href="\.\.\/css\/styles\.css"/', 'href="css/admin-modern.css"', $content);
    $content = preg_replace('/href="\<\?php echo SITE_URL; \?\>\/admin\/css\/.*?\.css"/', 'href="css/admin-modern.css"', $content);
    $content = preg_replace('/href="css\/enhanced-admin-styles\.css"/', 'href="css/admin-modern.css"', $content);
    $content = preg_replace('/href="css\/admin-styles\.css"/', 'href="css/admin-modern.css"', $content);
    
    // Add admin-fixes.css if admin-modern.css exists but admin-fixes.css doesn't
    if (strpos($content, 'admin-modern.css') !== false && strpos($content, 'admin-fixes.css') === false) {
        $content = str_replace(
            'href="css/admin-modern.css">',
            'href="css/admin-modern.css">' . "\n" . '    <link rel="stylesheet" href="css/admin-fixes.css">',
            $content
        );
    }
    
    // Fix notifications.css path
    $content = preg_replace('/href="\<\?php echo SITE_URL; \?\>\/css\/notifications\.css"/', 'href="../css/notifications.css"', $content);
    
    // Add notifications.css if not present
    if (strpos($content, 'notifications.css') === false && strpos($content, 'admin-modern.css') !== false) {
        $content = str_replace(
            'href="css/admin-fixes.css">',
            'href="css/admin-fixes.css">' . "\n" . '    <link rel="stylesheet" href="../css/notifications.css">',
            $content
        );
    }
    
    // Fix JS paths
    $content = preg_replace('/src="\<\?php echo SITE_URL; \?\>\/js\/notifications\.js"/', 'src="../js/notifications.js"', $content);
    
    // Add admin-modern.js if not present
    if (strpos($content, 'admin-modern.js') === false && strpos($content, '</body>') !== false) {
        $content = str_replace(
            '</body>',
            '    <script src="../js/notifications.js"></script>' . "\n" . 
            '    <script src="js/admin-modern.js"></script>' . "\n" . 
            '</body>',
            $content
        );
    }
    
    // Add header include if not present
    if (strpos($content, 'includes/header.php') === false && strpos($content, '<body>') !== false) {
        $content = str_replace(
            '<body>',
            '<body>' . "\n" . '    <?php include \'includes/header.php\'; ?>' . "\n",
            $content
        );
    }
    
    // Fix layout structure
    $content = str_replace('<div class="admin-layout">', '<div class="admin-container">', $content);
    $content = str_replace('<?php include \'includes/admin_header.php\'; ?>', '', $content);
    $content = str_replace('<?php include \'includes/admin_sidebar.php\'; ?>', '<?php include \'includes/sidebar.php\'; ?>', $content);
    $content = str_replace('<main class="admin-main">', '<div class="main-content">', $content);
    $content = str_replace('</main>', '</div>', $content);
    
    // Fix SITE_NAME references
    $content = preg_replace('/\<\?php echo SITE_NAME; \?\>/', '<?php echo defined(\'SITE_NAME\') ? SITE_NAME : \'لوحة التحكم\'; ?>', $content);
    
    // Add Google Fonts if not present
    if (strpos($content, 'fonts.googleapis.com') === false && strpos($content, '</head>') !== false) {
        $googleFonts = '    <link rel="preconnect" href="https://fonts.googleapis.com">' . "\n" .
                      '    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n" .
                      '    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">' . "\n";
        
        $content = str_replace('</head>', $googleFonts . '</head>', $content);
    }
    
    // Update page structure for modern design
    if (strpos($content, '<div class="admin-content">') !== false) {
        $content = preg_replace(
            '/<div class="admin-content">\s*<h1>(.*?)<\/h1>/',
            '<div class="page-header">' . "\n" . 
            '                <h1>$1</h1>' . "\n" . 
            '                <p>إدارة وتحكم في النظام</p>' . "\n" . 
            '            </div>',
            $content
        );
        $content = str_replace('<div class="admin-content">', '', $content);
    }
    
    // Add modern button classes
    $content = preg_replace('/class="btn btn-primary"/', 'class="btn btn-primary" onclick="openModal(\'addModal\')"', $content);
    
    // Save if changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Updated: $file\n";
    } else {
        echo "No changes: $file\n";
    }
}

echo "\nAll admin files update completed!\n";
echo "Please check each file manually for any specific modal implementations needed.\n";
?>
