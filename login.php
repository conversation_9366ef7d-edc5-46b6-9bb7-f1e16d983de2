<?php
require_once 'config/config.php';
require_once 'includes/database.php';
require_once 'includes/notification_helper.php';

$error = '';
$success = '';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // If it's an AJAX request, return JSO<PERSON>
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'المستخدم مسجل دخوله بالفعل',
            'redirect' => 'page/dashboard.php'
        ]);
        exit;
    }
    header('Location: page/dashboard.php');
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if it's an AJAX request
    $isAjax = (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ||
              (isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) ||
              (isset($_POST['ajax']) && $_POST['ajax'] == '1');

    $usernameOrEmail = trim($_POST['username_email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    if (empty($usernameOrEmail) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم/البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $userManager = new UserManager();

            // Debug: Check if user exists
            error_log("Attempting login for: " . $usernameOrEmail);

            $user = $userManager->authenticateUser($usernameOrEmail, $password);

            if ($user) {
                // Log successful login attempt
                $userManager->logLoginAttempt($usernameOrEmail, true, $_SERVER['REMOTE_ADDR']);

                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['login_time'] = time();

                // Handle remember me
                if ($rememberMe) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
                }

                $success = SUCCESS_MESSAGES['login_success'];

                // Handle AJAX request
                if ($isAjax) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => $success,
                        'redirect' => 'page/dashboard.php'
                    ]);
                    exit;
                }

                header('Location: page/dashboard.php');
                exit;

            } else {
                // Log failed login attempt
                $userManager->logLoginAttempt($usernameOrEmail, false, $_SERVER['REMOTE_ADDR']);
                $error = 'اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة';

                // Debug: Log the error
                error_log("Login failed for: " . $usernameOrEmail);
            }

        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تسجيل الدخول: ' . $e->getMessage();
            error_log("Login exception: " . $e->getMessage());
        }
    }

    // Handle AJAX error response
    if ($isAjax) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $error
        ]);
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <meta name="description" content="تسجيل الدخول إلى منصة سلسلة الدكتور لتعليم اللغة العربية">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/enhanced-ui.css">
    <link rel="stylesheet" href="css/loading-system.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/notifications.css">

</head>
<body>
    <!-- Loading Screen will be created by JavaScript -->

    <!-- Split Screen Container -->
    <div class="split-container">
        <!-- Left Side - Login Form -->
        <div class="split-left">
            <div class="form-container">
                <h1 class="form-title">تسجيل الدخول</h1>
                <p class="form-subtitle">مرحباً بك مرة أخرى في منصة سلسلة الدكتور</p>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form id="loginForm" method="POST" onsubmit="event.preventDefault(); submitLoginForm();">
                    <input type="hidden" name="ajax" value="1">
                    <div class="form-group">
                        <label for="username_email" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                        <input
                            type="text"
                            id="username_email"
                            name="username_email"
                            class="form-input"
                            required
                            placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                            value="<?php echo htmlspecialchars($_POST['username_email'] ?? ''); ?>"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            required
                            placeholder="أدخل كلمة المرور"
                        >
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="remember_me" name="remember_me">
                                <label for="remember_me">تذكرني</label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">
                        تسجيل الدخول
                    </button>

                    <div class="text-center mt-20">
                        <p>ليس لديك حساب؟ <a href="signup.php" style="color: #4682B4; text-decoration: none; font-weight: bold;">إنشاء حساب جديد</a></p>
                    </div>

                    <div class="text-center mt-10">
                        <a href="#" style="color: #6c757d; text-decoration: none; font-size: 14px;">نسيت كلمة المرور؟</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Right Side - Platform Features -->
        <div class="split-right">
            <div class="platform-features">
                <img src="img/logo-b.png" alt="شعار سلسلة الدكتور" class="platform-logo">
                <h1 class="platform-title">سلسلة الدكتور</h1>
                <p class="platform-subtitle">منصة تعليم اللغة العربية مع أفضل المعلمين<br>مستر محمد عبدالله</p>

                <!-- Welcome Message -->
                <div class="welcome-section">
                    <div class="welcome-title">🎓 مرحباً بك مرة أخرى</div>
                    <p class="welcome-text">سجل دخولك للوصول إلى دروسك ومتابعة تقدمك التعليمي</p>
                </div>

                <!-- Quick Stats -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">📚</div>
                        <div class="stat-number">50+</div>
                        <div class="stat-label">درس تفاعلي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">👨‍🎓</div>
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">طالب ناجح</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-number">15+</div>
                        <div class="stat-label">سنة خبرة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">🏆</div>
                        <div class="stat-number">95%</div>
                        <div class="stat-label">نسبة النجاح</div>
                    </div>
                </div>

                
                
            </div>
        </div>
    </div>

    <!-- Welcome Modal -->
    <div id="welcomeModal" class="modal">
        <div class="modal-content">
            <h2 class="modal-title">مرحباً بك في سلسلة الدكتور</h2>
            <p class="modal-text">
                منصة تعليمية متميزة لتعلم اللغة العربية مع أفضل المعلمين.<br>
                ابدأ رحلتك التعليمية معنا الآن!
            </p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="hideModal('welcomeModal')">ابدأ الآن</button>
            </div>
        </div>
    </div>

    <script src="<?php echo SITE_URL; ?>/js/notifications.js"></script>
    <script src="js/script.js"></script>
    <script src="js/enhanced-ui.js"></script>
    <script src="js/loading-system.js"></script>
</body>
</html>