<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$db = Database::getInstance()->getConnection();
$user_id = $_SESSION['user_id'];

// Get exercise ID
$exercise_id = isset($_GET['exercise_id']) ? (int)$_GET['exercise_id'] : 0;

if (!$exercise_id) {
    header('Location: curriculum.php');
    exit;
}

// Handle reset parameter
if (isset($_GET['reset']) && $_GET['reset'] == '1') {
    unset($_SESSION['exercise_results_' . $exercise_id]);
    header('Location: exercise.php?exercise_id=' . $exercise_id);
    exit;
}

// Get exercise info with lesson and subject
$stmt = $db->prepare("
    SELECT le.*, l.title as lesson_title, l.id as lesson_id, l.subject_id, l.is_free,
           cs.name as subject_name, cs.color as subject_color
    FROM lesson_exercises le
    JOIN lessons l ON le.lesson_id = l.id
    JOIN curriculum_subjects cs ON l.subject_id = cs.id
    WHERE le.id = ? AND le.is_active = 1 AND l.is_active = 1
");
$stmt->execute([$exercise_id]);
$exercise = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exercise) {
    header('Location: curriculum.php');
    exit;
}

// Check if user can access this exercise
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date
                     FROM users u WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

$can_access = $exercise['is_free'] || $has_subscription;

if (!$can_access) {
    header('Location: lesson_content.php?lesson_id=' . $exercise['lesson_id']);
    exit;
}

// Get exercise questions
try {
    $questions_stmt = $db->prepare("SELECT * FROM exercise_questions WHERE exercise_id = ? AND is_active = 1 ORDER BY question_order, id");
    $questions_stmt->execute([$exercise_id]);
    $questions = $questions_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $questions = [];
}

// Get user's exercise progress
try {
    $stmt = $db->prepare("SELECT * FROM user_exercise_progress WHERE user_id = ? AND exercise_id = ?");
    $stmt->execute([$user_id, $exercise_id]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $progress = null;
}

// Initialize variables
$exercise_completed = false;
$score = 0;
$correct_answers = 0;
$total_questions = count($questions);
$results = [];
$show_results = false;

// Check if we have fresh results from form submission
if (isset($_SESSION['exercise_results_' . $exercise_id])) {
    $session_results = $_SESSION['exercise_results_' . $exercise_id];
    $exercise_completed = true;
    $score = $session_results['score'];
    $correct_answers = $session_results['correct_answers'];
    $total_questions = $session_results['total_questions'];
    $results = $session_results['results'];
    $show_results = true;
    
    // Clear session after displaying
    unset($_SESSION['exercise_results_' . $exercise_id]);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_exercise']) && !$exercise_completed) {
    $answers = $_POST['answers'] ?? [];
    $total_questions = count($questions);
    $correct_answers = 0;
    $results = [];
    
    // Calculate score and prepare results
    foreach ($questions as $question) {
        $user_answer = $answers[$question['id']] ?? '';
        $is_correct = $user_answer === $question['correct_answer'];
        
        if ($is_correct) {
            $correct_answers++;
        }
        
        // Store result for each question
        $results[] = [
            'question' => $question,
            'user_answer' => $user_answer,
            'is_correct' => $is_correct
        ];
    }
    
    $score = $total_questions > 0 ? round(($correct_answers / $total_questions) * 100, 2) : 0;
    
    try {
        // Save or update progress
        if ($progress) {
            $stmt = $db->prepare("UPDATE user_exercise_progress SET is_completed = 1, score = ?, total_questions = ?, correct_answers = ?, completed_at = NOW() WHERE user_id = ? AND exercise_id = ?");
            $stmt->execute([$score, $total_questions, $correct_answers, $user_id, $exercise_id]);
        } else {
            $stmt = $db->prepare("INSERT INTO user_exercise_progress (user_id, exercise_id, is_completed, score, total_questions, correct_answers, completed_at) VALUES (?, ?, 1, ?, ?, ?, NOW())");
            $stmt->execute([$user_id, $exercise_id, $score, $total_questions, $correct_answers]);
        }
        
        // Update lesson progress
        updateLessonProgress($db, $user_id, $exercise['lesson_id']);
        
        // Store results in session for display
        $_SESSION['exercise_results_' . $exercise_id] = [
            'score' => $score,
            'correct_answers' => $correct_answers,
            'total_questions' => $total_questions,
            'results' => $results
        ];
        
        // Redirect to show results
        header('Location: exercise.php?exercise_id=' . $exercise_id);
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في حفظ النتيجة: " . $e->getMessage();
    }
}

function updateLessonProgress($db, $user_id, $lesson_id) {
    // Get total content count
    $total_query = "
        SELECT 
            (SELECT COUNT(*) FROM lesson_videos WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exercises WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exams WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_summaries WHERE lesson_id = ? AND is_active = 1) as total_count
    ";
    $stmt = $db->prepare($total_query);
    $stmt->execute([$lesson_id, $lesson_id, $lesson_id, $lesson_id]);
    $total_count = $stmt->fetchColumn();
    
    // Get completed content count
    try {
        $completed_query = "
            SELECT 
                (SELECT COUNT(*) FROM user_video_progress uvp 
                 JOIN lesson_videos lv ON uvp.video_id = lv.id 
                 WHERE uvp.user_id = ? AND lv.lesson_id = ? AND uvp.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exercise_progress uep 
                 JOIN lesson_exercises le ON uep.exercise_id = le.id 
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exam_progress uep 
                 JOIN lesson_exams le ON uep.exam_id = le.id 
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_summary_progress usp 
                 JOIN lesson_summaries ls ON usp.summary_id = ls.id 
                 WHERE usp.user_id = ? AND ls.lesson_id = ? AND usp.is_completed = 1) as completed_count
        ";
        $stmt = $db->prepare($completed_query);
        $stmt->execute([$user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id]);
        $completed_count = $stmt->fetchColumn();
    } catch (Exception $e) {
        $completed_count = 0;
    }
    
    $completion_percentage = $total_count > 0 ? round(($completed_count / $total_count) * 100, 2) : 0;
    $is_completed = $completion_percentage >= 100;
    
    // Update or insert lesson progress
    try {
        $stmt = $db->prepare("
            INSERT INTO user_lesson_progress (user_id, lesson_id, completion_percentage, is_completed, last_accessed_at, completed_at)
            VALUES (?, ?, ?, ?, NOW(), ?)
            ON DUPLICATE KEY UPDATE 
                completion_percentage = VALUES(completion_percentage),
                is_completed = VALUES(is_completed),
                last_accessed_at = VALUES(last_accessed_at),
                completed_at = CASE WHEN VALUES(is_completed) = 1 AND is_completed = 0 THEN NOW() ELSE completed_at END
        ");
        $stmt->execute([$user_id, $lesson_id, $completion_percentage, $is_completed, $is_completed ? date('Y-m-d H:i:s') : null]);
    } catch (Exception $e) {
        // Ignore errors for now
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($exercise['title']); ?> - تدريب - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="exercise-container">
                <!-- Exercise Header -->
                <div class="exercise-header">
                    <div class="exercise-info">
                        <div class="exercise-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <div class="exercise-details">
                            <h1><?php echo htmlspecialchars($exercise['title']); ?></h1>
                            <div class="exercise-meta">
                                <span class="lesson-badge">
                                    <?php echo htmlspecialchars($exercise['lesson_title']); ?>
                                </span>
                                <span class="subject-badge" style="background-color: <?php echo $exercise['subject_color']; ?>20; color: <?php echo $exercise['subject_color']; ?>;">
                                    <?php echo htmlspecialchars($exercise['subject_name']); ?>
                                </span>
                                <?php if ($progress && $progress['is_completed']): ?>
                                    <span class="completion-badge">
                                        <i class="fas fa-check-circle"></i>
                                        مكتمل - <?php echo round($progress['score'], 1); ?>%
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php if ($exercise['description']): ?>
                                <p class="exercise-description"><?php echo htmlspecialchars($exercise['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="exercise-actions">
                        <a href="lesson_content.php?lesson_id=<?php echo $exercise['lesson_id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للدرس
                        </a>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($exercise_completed && $show_results): ?>
                    <!-- Results Section -->
                    <div class="result-card">
                        <div class="result-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="result-content">
                            <h2>تم إكمال التدريب!</h2>
                            <div class="score-display">
                                <span class="score-number"><?php echo round($score, 1); ?>%</span>
                                <span class="score-label">النتيجة النهائية</span>
                            </div>
                            <div class="score-details">
                                <span><?php echo $correct_answers; ?> من <?php echo $total_questions; ?> إجابة صحيحة</span>
                            </div>
                            <div class="result-actions">
                                <a href="lesson_content.php?lesson_id=<?php echo $exercise['lesson_id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للدرس
                                </a>
                                <a href="exercise.php?exercise_id=<?php echo $exercise_id; ?>&reset=1" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i>
                                    إعادة التدريب
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Results -->
                    <?php if (!empty($results)): ?>
                    <div class="detailed-results">
                        <div class="results-header">
                            <h3><i class="fas fa-list-check"></i> مراجعة الإجابات</h3>
                        </div>
                        <div class="results-container">
                            <?php foreach ($results as $index => $result): ?>
                                <div class="result-question <?php echo $result['is_correct'] ? 'correct' : 'incorrect'; ?>">
                                    <div class="result-question-header">
                                        <div class="question-info">
                                            <span class="question-number">السؤال <?php echo $index + 1; ?></span>
                                            <span class="result-status">
                                                <?php if ($result['is_correct']): ?>
                                                    <i class="fas fa-check-circle"></i>
                                                    إجابة صحيحة
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle"></i>
                                                    إجابة خاطئة
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="result-question-content">
                                        <h4><?php echo htmlspecialchars($result['question']['question_text']); ?></h4>

                                        <?php if ($result['question']['question_type'] === 'true_false'): ?>
                                            <div class="answer-review">
                                                <div class="answer-item user-answer <?php echo $result['is_correct'] ? 'correct' : 'incorrect'; ?>">
                                                    <span class="answer-label">إجابتك:</span>
                                                    <span class="answer-value"><?php echo $result['user_answer'] === 'true' ? 'صح' : 'خطأ'; ?></span>
                                                </div>
                                                <?php if (!$result['is_correct']): ?>
                                                    <div class="answer-item correct-answer">
                                                        <span class="answer-label">الإجابة الصحيحة:</span>
                                                        <span class="answer-value"><?php echo $result['question']['correct_answer'] === 'true' ? 'صح' : 'خطأ'; ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <?php
                                            $options = json_decode($result['question']['options'], true);
                                            $option_labels = ['أ', 'ب', 'ج', 'د'];
                                            $option_keys = ['option_a', 'option_b', 'option_c', 'option_d'];
                                            ?>
                                            <div class="options-review">
                                                <?php foreach ($option_keys as $i => $key): ?>
                                                    <?php if (!empty($options[$key])): ?>
                                                        <div class="option-review <?php
                                                            if ($key === $result['question']['correct_answer']) echo 'correct-option';
                                                            elseif ($key === $result['user_answer'] && !$result['is_correct']) echo 'user-wrong-option';
                                                            elseif ($key === $result['user_answer'] && $result['is_correct']) echo 'user-correct-option';
                                                        ?>">
                                                            <span class="option-letter"><?php echo $option_labels[$i]; ?>)</span>
                                                            <span class="option-text"><?php echo htmlspecialchars($options[$key]); ?></span>
                                                            <?php if ($key === $result['question']['correct_answer']): ?>
                                                                <i class="fas fa-check-circle option-icon"></i>
                                                            <?php elseif ($key === $result['user_answer'] && !$result['is_correct']): ?>
                                                                <i class="fas fa-times-circle option-icon"></i>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($result['question']['explanation']): ?>
                                            <div class="explanation">
                                                <h5><i class="fas fa-lightbulb"></i> شرح الإجابة:</h5>
                                                <p><?php echo htmlspecialchars($result['question']['explanation']); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                <?php elseif (empty($questions)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">❓</div>
                        <h2>لا توجد أسئلة</h2>
                        <p>لم يتم إضافة أسئلة لهذا التدريب بعد</p>
                        <a href="lesson_content.php?lesson_id=<?php echo $exercise['lesson_id']; ?>" class="btn btn-primary">العودة للدرس</a>
                    </div>

                <?php else: ?>
                    <!-- Exercise Form -->
                    <form method="POST" class="exercise-form" id="exerciseForm">
                        <div class="questions-container">
                            <?php foreach ($questions as $index => $question): ?>
                                <div class="question-card">
                                    <div class="question-header">
                                        <span class="question-number">السؤال <?php echo $index + 1; ?></span>
                                        <span class="question-type">
                                            <?php if ($question['question_type'] === 'true_false'): ?>
                                                <span class="badge badge-info">صح وخطأ</span>
                                            <?php else: ?>
                                                <span class="badge badge-warning">اختيار متعدد</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="question-content">
                                        <h3><?php echo htmlspecialchars($question['question_text']); ?></h3>

                                        <div class="answer-options">
                                            <?php if ($question['question_type'] === 'true_false'): ?>
                                                <label class="option-label">
                                                    <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="true" required>
                                                    <span class="option-text">صح</span>
                                                </label>
                                                <label class="option-label">
                                                    <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="false" required>
                                                    <span class="option-text">خطأ</span>
                                                </label>
                                            <?php else: ?>
                                                <?php
                                                $options = json_decode($question['options'], true);
                                                $option_labels = ['أ', 'ب', 'ج', 'د'];
                                                $option_keys = ['option_a', 'option_b', 'option_c', 'option_d'];
                                                ?>
                                                <?php foreach ($option_keys as $i => $key): ?>
                                                    <?php if (!empty($options[$key])): ?>
                                                        <label class="option-label">
                                                            <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="<?php echo $key; ?>" required>
                                                            <span class="option-letter"><?php echo $option_labels[$i]; ?>)</span>
                                                            <span class="option-text"><?php echo htmlspecialchars($options[$key]); ?></span>
                                                        </label>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="exercise-footer">
                            <button type="submit" name="submit_exercise" class="btn btn-primary btn-large">
                                <i class="fas fa-check"></i>
                                إرسال الإجابات
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .exercise-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(135, 206, 235, 0.02) 0%,
                rgba(70, 130, 180, 0.05) 50%,
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .exercise-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .exercise-info {
            display: flex;
            align-items: flex-start;
            gap: 25px;
        }

        .exercise-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            flex-shrink: 0;
        }

        .exercise-details h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .exercise-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .lesson-badge, .subject-badge, .completion-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .completion-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .exercise-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        .result-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            margin-bottom: 30px;
        }

        .result-icon {
            font-size: 4rem;
            color: #ffc107;
            margin-bottom: 20px;
        }

        .result-content h2 {
            color: #28a745;
            margin: 0 0 25px 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .score-display {
            margin: 25px 0;
        }

        .score-number {
            font-size: 3rem;
            font-weight: bold;
            color: #28a745;
            display: block;
            line-height: 1;
        }

        .score-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 8px;
            display: block;
        }

        .score-details {
            color: #6c757d;
            margin: 20px 0 30px 0;
            font-size: 16px;
        }

        .result-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .questions-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(135, 206, 235, 0.2);
            transition: all 0.3s ease;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .question-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .question-number {
            font-weight: 600;
            font-size: 16px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .question-content {
            padding: 25px;
        }

        .question-content h3 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.5;
        }

        .answer-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .option-label {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option-label:hover {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
        }

        .option-label input[type="radio"] {
            width: 18px;
            height: 18px;
            accent-color: #28a745;
        }

        .option-letter {
            font-weight: bold;
            color: #28a745;
            min-width: 20px;
        }

        .option-text {
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.4;
        }

        .exercise-footer {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-state h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Detailed Results Styles */
        .detailed-results {
            margin-top: 30px;
        }

        .results-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .results-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .results-container {
            background: white;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .result-question {
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .result-question:last-child {
            border-bottom: none;
        }

        .result-question.correct {
            border-left: 4px solid #28a745;
        }

        .result-question.incorrect {
            border-left: 4px solid #dc3545;
        }

        .result-question-header {
            padding: 20px 25px 15px 25px;
            background: #f8f9fa;
        }

        .question-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .result-question.correct .result-status {
            color: #28a745;
        }

        .result-question.incorrect .result-status {
            color: #dc3545;
        }

        .result-question-content {
            padding: 20px 25px;
        }

        .result-question-content h4 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.5;
        }

        .answer-review {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .answer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .answer-item.user-answer.correct {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .answer-item.user-answer.incorrect {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #721c24;
        }

        .answer-item.correct-answer {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .answer-label {
            font-weight: 600;
            min-width: 100px;
        }

        .answer-value {
            font-weight: 500;
        }

        .options-review {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
        }

        .option-review {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .option-review.correct-option {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #155724;
        }

        .option-review.user-wrong-option {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
            color: #721c24;
        }

        .option-review.user-correct-option {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #155724;
        }

        .option-review .option-letter {
            font-weight: bold;
            min-width: 20px;
        }

        .option-review.correct-option .option-letter {
            color: #28a745;
        }

        .option-review.user-wrong-option .option-letter {
            color: #dc3545;
        }

        .option-review .option-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }

        .option-icon {
            font-size: 16px;
        }

        .option-review.correct-option .option-icon {
            color: #28a745;
        }

        .option-review.user-wrong-option .option-icon {
            color: #dc3545;
        }

        .explanation {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .explanation h5 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .explanation p {
            color: #856404;
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .exercise-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .exercise-info {
                flex-direction: column;
                text-align: center;
            }

            .exercise-details h1 {
                font-size: 1.5rem;
            }

            .exercise-meta {
                justify-content: center;
            }

            .result-actions {
                flex-direction: column;
                align-items: center;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
