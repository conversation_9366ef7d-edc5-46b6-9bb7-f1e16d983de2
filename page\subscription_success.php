<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Get success details from URL
$plan_name = isset($_GET['plan_name']) ? $_GET['plan_name'] : 'الخطة';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
$method = isset($_GET['method']) ? $_GET['method'] : 'unknown';

$method_names = [
    'code' => 'كود السنتر',
    'fawry' => 'فوري',
    'visa' => 'فيزا/ماستركارد',
    'wallet' => 'المحفظة الإلكترونية'
];

$method_name = isset($method_names[$method]) ? $method_names[$method] : 'غير محدد';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم الاشتراك بنجاح - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <!-- Celebration Background -->
    <div class="celebration-bg">
        <div class="stars-container" id="starsContainer"></div>
        <div class="confetti-container" id="confettiContainer"></div>
    </div>
    
    <div class="page-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            
            <h1>🎉 تهانينا! تم الاشتراك بنجاح</h1>
            <p class="success-subtitle">مرحباً بك في عائلة المتعلمين المتميزين</p>
            
            <div class="subscription-details">
                <div class="detail-item">
                    <i class="fas fa-crown"></i>
                    <div>
                        <h3>الخطة المفعلة</h3>
                        <p><?php echo htmlspecialchars($plan_name); ?></p>
                    </div>
                </div>
                
                <div class="detail-item">
                    <i class="fas fa-calendar-alt"></i>
                    <div>
                        <h3>صالح حتى</h3>
                        <p><?php echo $end_date ? date('Y-m-d', strtotime($end_date)) : 'غير محدد'; ?></p>
                    </div>
                </div>
                
                <div class="detail-item">
                    <i class="fas fa-credit-card"></i>
                    <div>
                        <h3>طريقة التفعيل</h3>
                        <p><?php echo $method_name; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="success-message">
                <h2>🚀 ماذا بعد؟</h2>
                <div class="next-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>استكشف المحتوى</h4>
                            <p>تصفح جميع الأقسام والدروس المتاحة</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>ابدأ التعلم</h4>
                            <p>اختر القسم المناسب وابدأ رحلتك التعليمية</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>تابع تقدمك</h4>
                            <p>راقب إنجازاتك من خلال لوحة التحكم</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="dashboard.php" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i>
                    الذهاب للداشبورد
                </a>
                
                <a href="curriculum.php" class="btn btn-success">
                    <i class="fas fa-book-open"></i>
                    استكشف المنهج
                </a>
            </div>
            
            <div class="support-info">
                <i class="fas fa-headset"></i>
                <p>تحتاج مساعدة؟ تواصل معنا على <strong>01126130559</strong></p>
            </div>
        </div>
    </div>

    <script>
        // Create stars animation
        function createStars() {
            const container = document.getElementById('starsContainer');
            const starCount = 50;
            
            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.innerHTML = '⭐';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 3 + 2) + 's';
                container.appendChild(star);
            }
        }

        // Create confetti animation
        function createConfetti() {
            const container = document.getElementById('confettiContainer');
            const confettiCount = 30;
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
            
            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.animationDelay = Math.random() * 3 + 's';
                confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                container.appendChild(confetti);
            }
        }

        // Initialize animations
        document.addEventListener('DOMContentLoaded', function() {
            createStars();
            createConfetti();
            
            // Auto-hide celebration after 5 seconds
            setTimeout(() => {
                document.querySelector('.celebration-bg').style.opacity = '0.3';
            }, 5000);
        });
    </script>

    <style>
        body {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 50%, #87CEEB 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(70, 130, 180, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(32, 178, 170, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(135, 206, 235, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .celebration-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            transition: opacity 2s ease;
        }

        .stars-container, .confetti-container {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .star {
            position: absolute;
            font-size: 20px;
            animation: twinkle infinite ease-in-out;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            animation: fall infinite linear;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0; transform: scale(0.5) rotate(0deg); }
            50% { opacity: 1; transform: scale(1) rotate(180deg); }
        }

        @keyframes fall {
            0% { transform: translateY(-100vh) rotate(0deg); }
            100% { transform: translateY(100vh) rotate(360deg); }
        }

        .page-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .success-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 50px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            font-size: 5rem;
            color: #28a745;
            margin-bottom: 20px;
            animation: bounce 1s ease-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        .success-card h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .success-subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 40px;
        }

        .subscription-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .detail-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .detail-item i {
            font-size: 2rem;
            color: #4682B4;
        }

        .detail-item h3 {
            color: #333;
            margin: 0 0 5px 0;
            font-size: 16px;
        }

        .detail-item p {
            color: #666;
            margin: 0;
            font-weight: bold;
        }

        .success-message {
            margin-bottom: 40px;
        }

        .success-message h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        .next-steps {
            display: flex;
            flex-direction: column;
            gap: 20px;
            text-align: right;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        .step-content h4 {
            color: #1565c0;
            margin: 0 0 5px 0;
            font-size: 18px;
        }

        .step-content p {
            color: #1976d2;
            margin: 0;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            min-width: 200px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(70, 130, 180, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        }

        .support-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: center;
        }

        .support-info i {
            font-size: 1.5rem;
            color: #856404;
        }

        .support-info p {
            color: #856404;
            margin: 0;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .success-card {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .success-card h1 {
                font-size: 2rem;
            }
            
            .subscription-details {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .step {
                flex-direction: column;
                text-align: center;
            }
            
            .next-steps {
                text-align: center;
            }
        }
    </style>

    <?php include '../includes/footer.php'; ?>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
