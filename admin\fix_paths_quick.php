<?php
/**
 * Quick fix for admin file paths
 */

// Files to update
$files = [
    'users.php',
    'courses.php', 
    'news.php',
    'exams.php',
    'manage_notifications.php',
    'send_notifications.php',
    'activation_codes.php',
    'payment_requests.php',
    'student_notes.php',
    'add_admin.php'
];

foreach ($files as $file) {
    $filePath = __DIR__ . '/' . $file;
    
    if (!file_exists($filePath)) {
        echo "File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Fix CSS paths
    $content = str_replace('href="css/admin-modern.css"', 'href="css/admin-modern.css"', $content);
    $content = str_replace('href="css/admin-modern.css"', 'href="css/admin-modern.css"', $content);
    $content = str_replace('href="<?php echo SITE_URL; ?>/admin/css/admin-styles.css"', 'href="css/admin-modern.css"', $content);
    
    // Add admin-fixes.css if admin-modern.css exists but admin-fixes.css doesn't
    if (strpos($content, 'admin-modern.css') !== false && strpos($content, 'admin-fixes.css') === false) {
        $content = str_replace(
            'href="css/admin-modern.css">',
            'href="css/admin-modern.css">' . "\n" . '    <link rel="stylesheet" href="css/admin-fixes.css">',
            $content
        );
    }
    
    // Fix notifications.css path
    $content = str_replace('href="<?php echo SITE_URL; ?>/css/notifications.css"', 'href="../css/notifications.css"', $content);
    
    // Fix JS paths
    $content = str_replace('src="<?php echo SITE_URL; ?>/js/notifications.js"', 'src="../js/notifications.js"', $content);
    
    // Add admin-modern.js if not present
    if (strpos($content, 'admin-modern.js') === false && strpos($content, '</body>') !== false) {
        $content = str_replace(
            '</body>',
            '    <script src="js/admin-modern.js"></script>' . "\n" . '</body>',
            $content
        );
    }
    
    // Add header include if not present
    if (strpos($content, 'includes/header.php') === false && strpos($content, '<body>') !== false) {
        $content = str_replace(
            '<body>',
            '<body>' . "\n" . '    <?php include \'includes/header.php\'; ?>' . "\n",
            $content
        );
    }
    
    // Fix SITE_NAME references
    $content = str_replace('<?php echo SITE_NAME; ?>', '<?php echo defined(\'SITE_NAME\') ? SITE_NAME : \'لوحة التحكم\'; ?>', $content);
    
    // Add Google Fonts if not present
    if (strpos($content, 'fonts.googleapis.com') === false && strpos($content, '</head>') !== false) {
        $googleFonts = '    <link rel="preconnect" href="https://fonts.googleapis.com">' . "\n" .
                      '    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n" .
                      '    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">' . "\n";
        
        $content = str_replace('</head>', $googleFonts . '</head>', $content);
    }
    
    // Save if changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Updated: $file\n";
    } else {
        echo "No changes: $file\n";
    }
}

echo "\nQuick path fix completed!\n";
?>
