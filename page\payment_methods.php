<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Get plan details from URL
$plan_id = isset($_GET['plan_id']) ? intval($_GET['plan_id']) : 0;
$plan_name = isset($_GET['plan_name']) ? $_GET['plan_name'] : '';
$price = isset($_GET['price']) ? floatval($_GET['price']) : 0;

if (!$plan_id || !$plan_name || !$price) {
    header('Location: subscriptions.php');
    exit;
}

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if user has active subscription
    $stmt = $db->prepare("SELECT subscription_status, subscription_end_date, current_plan_id FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    $has_active_subscription = $user && $user['subscription_status'] === 'active' &&
                              $user['subscription_end_date'] &&
                              strtotime($user['subscription_end_date']) > time();

    // If user has active subscription, redirect to subscriptions page
    if ($has_active_subscription) {
        header('Location: subscriptions.php?error=active_subscription');
        exit;
    }

    // Get plan details
    $stmt = $db->prepare("SELECT * FROM subscription_plans WHERE id = ? AND is_active = 1");
    $stmt->execute([$plan_id]);
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$plan) {
        header('Location: subscriptions.php');
        exit;
    }

} catch (Exception $e) {
    header('Location: subscriptions.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طرق الدفع - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="page-container">
        <div class="page-header">
            <h1>اختر طريقة الدفع</h1>
            <p>اختر الطريقة المناسبة لك لإتمام الاشتراك</p>
        </div>

        <!-- Selected Plan Summary -->
        <div class="plan-summary">
            <div class="summary-card">
                <div class="summary-header">
                    <div class="plan-icon" style="background-color: <?php echo isset($plan['color']) ? $plan['color'] : '#4682B4'; ?>20; color: <?php echo isset($plan['color']) ? $plan['color'] : '#4682B4'; ?>">
                        <?php echo isset($plan['icon']) ? $plan['icon'] : '📚'; ?>
                    </div>
                    <div class="summary-info">
                        <h3>الخطة المختارة</h3>
                        <h2><?php echo htmlspecialchars(isset($plan['name']) ? $plan['name'] : $plan_name); ?></h2>
                        <div class="summary-price">
                            <?php if (isset($plan['discount_percentage']) && $plan['discount_percentage'] > 0): ?>
                                <span class="original-price"><?php echo number_format(isset($plan['price']) ? $plan['price'] : $price, 0); ?> جنيه</span>
                                <span class="discount-badge"><?php echo $plan['discount_percentage']; ?>% خصم</span>
                            <?php endif; ?>
                            <span class="current-price"><?php echo number_format(isset($plan['discounted_price']) ? $plan['discounted_price'] : $price, 0); ?> جنيه</span>
                            <span class="duration">لمدة <?php echo isset($plan['duration_days']) ? $plan['duration_days'] : '30'; ?> يوم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="payment-methods">
            <h2>طرق الدفع المتاحة</h2>
            
            <div class="payment-methods-grid">
                <!-- Center Code Method -->
                <div class="payment-option center-code-option">
                    <div class="payment-option-header">
                        <div class="payment-icon-container">
                            <div class="payment-icon center-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                        </div>
                        <div class="payment-option-info">
                            <h3>كود السنتر</h3>
                            <p>للطلاب المسجلين في المراكز التعليمية</p>
                        </div>
                        <div class="payment-badge instant">
                            <span>فوري</span>
                        </div>
                    </div>

                    <div class="payment-features">
                        <div class="feature-item">
                            <span class="feature-icon">⚡</span>
                            <span>تفعيل فوري خلال ثوانٍ</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💰</span>
                            <span>بدون رسوم إضافية</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">👨‍🏫</span>
                            <span>دعم مباشر من المعلم</span>
                        </div>
                    </div>

                    <button class="payment-button center-button" onclick="selectPaymentMethod('center_code')">
                        <span class="button-content">
                            <span class="button-icon">🔑</span>
                            <span class="button-text">تفعيل بالكود</span>
                        </span>
                        <span class="button-arrow">→</span>
                    </button>
                </div>

                <!-- Fawry Method -->
                <div class="payment-option fawry-option">
                    <div class="payment-option-header">
                        <div class="payment-icon-container">
                            <div class="payment-icon fawry-icon">
                                <i class="fas fa-store"></i>
                            </div>
                        </div>
                        <div class="payment-option-info">
                            <h3>فوري</h3>
                            <p>ادفع نقداً من أقرب نقطة فوري</p>
                        </div>
                        <div class="payment-badge popular">
                            <span>شائع</span>
                        </div>
                    </div>

                    <div class="payment-features">
                        <div class="feature-item">
                            <span class="feature-icon">🏪</span>
                            <span>متاح في جميع أنحاء مصر</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💵</span>
                            <span>دفع نقدي آمن ومضمون</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⏱️</span>
                            <span>تفعيل خلال دقائق معدودة</span>
                        </div>
                    </div>

                    <button class="payment-button fawry-button" onclick="selectPaymentMethod('fawry')">
                        <span class="button-content">
                            <span class="button-icon">💳</span>
                            <span class="button-text">ادفع بفوري</span>
                        </span>
                        <span class="button-arrow">→</span>
                    </button>
                </div>

                <!-- Cards Method -->
                <div class="payment-option cards-option featured">
                    <div class="payment-option-header">
                        <div class="payment-icon-container">
                            <div class="payment-icon cards-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                        </div>
                        <div class="payment-option-info">
                            <h3>البطاقات البنكية</h3>
                            <p>فيزا • ماستركارد • ميزة</p>
                        </div>
                        <div class="payment-badge secure">
                            <span>آمن</span>
                        </div>
                    </div>

                    <div class="supported-cards-display">
                        <div class="card-brand visa">
                            <i class="fab fa-cc-visa"></i>
                            <span>Visa</span>
                        </div>
                        <div class="card-brand mastercard">
                            <i class="fab fa-cc-mastercard"></i>
                            <span>Mastercard</span>
                        </div>
                        <div class="card-brand meeza">
                            <i class="fas fa-credit-card"></i>
                            <span>ميزة</span>
                        </div>
                    </div>

                    <div class="payment-features">
                        <div class="feature-item">
                            <span class="feature-icon">🔒</span>
                            <span>تشفير SSL 256-bit للحماية القصوى</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⚡</span>
                            <span>تفعيل فوري خلال ثوانٍ</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🏦</span>
                            <span>يدعم جميع البنوك المصرية</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📱</span>
                            <span>متوافق مع الجوال والكمبيوتر</span>
                        </div>
                    </div>

                    <div class="security-indicators">
                        <div class="security-item">
                            <span class="security-icon">🛡️</span>
                            <span>PCI DSS</span>
                        </div>
                        <div class="security-item">
                            <span class="security-icon">🔐</span>
                            <span>3D Secure</span>
                        </div>
                        <div class="security-item">
                            <span class="security-icon">✅</span>
                            <span>Paymob</span>
                        </div>
                    </div>

                    <button class="payment-button cards-button featured-button" onclick="selectPaymentMethod('visa')">
                        <span class="button-content">
                            <span class="button-icon">💳</span>
                            <span class="button-text">ادفع بالبطاقة</span>
                        </span>
                        <span class="button-arrow">→</span>
                    </button>
                </div>

                <!-- E-Wallet Method -->
                <div class="payment-option wallet-option">
                    <div class="payment-option-header">
                        <div class="payment-icon-container">
                            <div class="payment-icon wallet-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                        </div>
                        <div class="payment-option-info">
                            <h3>المحفظة الإلكترونية</h3>
                            <p>فودافون كاش • أورانج كاش • إتصالات كاش</p>
                        </div>
                        <div class="payment-badge fast">
                            <span>سريع</span>
                        </div>
                    </div>

                    <div class="payment-features">
                        <div class="feature-item">
                            <span class="feature-icon">📱</span>
                            <span>دفع سريع من الموبايل</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⚡</span>
                            <span>تفعيل فوري بدون انتظار</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🚫</span>
                            <span>بدون الحاجة لبطاقات ائتمانية</span>
                        </div>
                    </div>

                    <button class="payment-button wallet-button" onclick="selectPaymentMethod('wallet')">
                        <span class="button-content">
                            <span class="button-icon">📲</span>
                            <span class="button-text">ادفع بالمحفظة</span>
                        </span>
                        <span class="button-arrow">→</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Security Notice -->
        <div class="security-notice-section">
            <div class="security-notice-content">
                <div class="security-notice-icon">
                    <i class="fas fa-shield-check"></i>
                </div>
                <div class="security-notice-text">
                    <h3>🔒 الأمان والحماية</h3>
                    <div class="security-features">
                        <div class="security-feature">
                            <i class="fas fa-lock"></i>
                            <span><strong>تشفير SSL 256-bit:</strong> جميع المعاملات محمية بأعلى مستويات التشفير</span>
                        </div>
                        <div class="security-feature">
                            <i class="fas fa-credit-card"></i>
                            <span><strong>PCI DSS Compliant:</strong> نتبع معايير الأمان العالمية للبطاقات</span>
                        </div>
                        <div class="security-feature">
                            <i class="fas fa-user-shield"></i>
                            <span><strong>حماية البيانات:</strong> لا نحتفظ ببيانات البطاقة على خوادمنا</span>
                        </div>
                        <div class="security-feature">
                            <i class="fas fa-mobile-alt"></i>
                            <span><strong>3D Secure:</strong> طبقة حماية إضافية للمعاملات الآمنة</span>
                        </div>
                        <div class="security-feature">
                            <i class="fas fa-check-circle"></i>
                            <span><strong>Paymob Gateway:</strong> بوابة دفع معتمدة تدعم فيزا، ماستركارد، وميزة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="page-actions">
            <a href="subscriptions.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للخطط
            </a>
        </div>
    </div>

    <script>
        function selectPaymentMethod(method) {
            const planId = <?php echo $plan_id; ?>;
            const planName = '<?php echo htmlspecialchars($plan_name); ?>';
            const price = <?php echo $price; ?>;

            if (method === 'center_code') {
                window.location.href = `activate_code.php?plan_id=${planId}&plan_name=${encodeURIComponent(planName)}&price=${price}`;
                return;
            }

            // Show loading overlay
            showLoading('جاري تحضير عملية الدفع...');

            // Prepare payment data
            const paymentData = {
                plan_id: planId,
                amount: price,
                payment_type: method === 'fawry' ? 'fawry' : (method === 'visa' ? 'card' : 'wallet'),
                wallet_type: method === 'wallet' ? 'vodafone_cash' : null
            };

            // Choose API endpoint
            const apiEndpoint = method === 'fawry' ? '../api/fawry_payment.php' : '../api/paymob_payment.php';

            // Send payment request
            fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData)
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    if (data.payment_url) {
                        // Redirect to payment gateway
                        window.location.href = data.payment_url;
                    } else {
                        alert('تم إنشاء طلب الدفع بنجاح. رقم المرجع: ' + (data.reference_number || data.order_id));
                    }
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                hideLoading();
                alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
                console.error('Payment error:', error);
            });
        }

        function showLoading(message) {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p>${message}</p>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }

        function hideLoading() {
            const loadingDiv = document.getElementById('loading-overlay');
            if (loadingDiv) {
                document.body.removeChild(loadingDiv);
            }
        }
    </script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 50%, #87CEEB 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(70, 130, 180, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(32, 178, 170, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(135, 206, 235, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .page-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 0;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.9) 0%, 
                rgba(248, 249, 250, 0.8) 100%);
            border-radius: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .page-header h1 {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .plan-summary {
            margin-bottom: 40px;
        }

        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-top: 4px solid <?php echo isset($plan['color']) ? $plan['color'] : '#4682B4'; ?>;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
        }

        .summary-info h3 {
            color: #666;
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .summary-info h2 {
            color: #333;
            margin: 0 0 15px 0;
            font-size: 1.8rem;
        }

        .summary-price {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        .discount-badge {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .current-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }

        .duration {
            color: #666;
            font-size: 14px;
        }

        /* Modern Payment Methods Design */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .payment-methods {
            margin-bottom: 50px;
        }

        .payment-methods h2 {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .payment-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
        }

        /* Payment Option Cards */
        .payment-option {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            padding: 0;
            box-shadow:
                0 16px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
        }

        .payment-option:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 24px 48px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.4);
        }

        .payment-option.featured {
            border: 2px solid #ffd700;
            transform: scale(1.02);
        }

        .payment-option.featured:hover {
            transform: translateY(-8px) scale(1.05);
        }

        /* Payment Option Header */
        .payment-option-header {
            padding: 24px 24px 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .payment-icon-container {
            position: relative;
        }

        .payment-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }

        .center-icon {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .fawry-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .cards-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .wallet-icon {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .payment-option-info {
            flex: 1;
        }

        .payment-option-info h3 {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 4px 0;
        }

        .payment-option-info p {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }

        .payment-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .payment-badge.instant {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .payment-badge.popular {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }

        .payment-badge.secure {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .payment-badge.fast {
            background: linear-gradient(135deg, #9f7aea, #805ad5);
            color: white;
        }

        /* Payment Features */
        .payment-features {
            padding: 20px 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            transition: all 0.2s ease;
        }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            padding: 8px 12px;
        }

        .feature-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .feature-item span:last-child {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Supported Cards Display */
        .supported-cards-display {
            padding: 16px 24px;
            display: flex;
            justify-content: center;
            gap: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-brand {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 12px;
            border-radius: 12px;
            background: rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
        }

        .card-brand:hover {
            transform: scale(1.05);
            background: rgba(0, 0, 0, 0.05);
        }

        .card-brand.visa i {
            color: #1A1F71;
            font-size: 24px;
        }

        .card-brand.mastercard i {
            color: #EB001B;
            font-size: 24px;
        }

        .card-brand.meeza i {
            color: #00A651;
            font-size: 24px;
        }

        .card-brand span {
            font-size: 11px;
            font-weight: 600;
            color: #4a5568;
        }

        /* Security Indicators */
        .security-indicators {
            padding: 16px 24px;
            display: flex;
            justify-content: center;
            gap: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .security-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(72, 187, 120, 0.1);
        }

        .security-icon {
            font-size: 16px;
        }

        .security-item span:last-child {
            font-size: 10px;
            font-weight: 600;
            color: #38a169;
        }

        .center-code .method-icon {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .fawry .method-icon {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .visa .method-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .wallet .method-icon {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        /* Payment Buttons */
        .payment-button {
            width: 100%;
            padding: 18px 28px;
            margin: 28px 0 0 0;
            border: none;
            border-radius: 16px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 32px rgba(0,0,0,0.10), 0 2px 8px rgba(0,0,0,0.04);
            letter-spacing: 0.5px;
        }

        .button-content {
            display: flex;
            align-items: center;
            gap: 12px;
            border-radius: 100px;
        }

        .button-icon {
            font-size: 22px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.10));
        }

        .button-text {
            font-weight: 700;
            font-size: 1.1em;
            letter-spacing: 0.2px;
        }

        .button-arrow {
            font-size: 22px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-right: 6px;
        }

        .center-button {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: #fff;
            box-shadow: 0 8px 24px rgba(23, 162, 184, 0.18);
        }

        .fawry-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #fff;
            box-shadow: 0 8px 24px rgba(40, 167, 69, 0.18);
        }

        .cards-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: #fff;
            box-shadow: 0 8px 24px rgba(0, 123, 255, 0.18);
        }

        .wallet-button {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #fff;
            box-shadow: 0 8px 24px rgba(255, 193, 7, 0.18);
        }

        .featured-button {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            box-shadow: 0 12px 32px rgba(70, 130, 180, 0.25);
        }

        .payment-button:hover {
            transform: translateY(-4px) scale(1.025);
            box-shadow: 0 16px 40px rgba(0,0,0,0.13), 0 4px 12px rgba(0,0,0,0.06);
        }

        .payment-button:hover .button-arrow {
            transform: translateX(-8px) scale(1.1);
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .payment-button:hover::before {
            left: 100%;
        }

        .method-header h3 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }

        .method-header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }

        .method-features {
            margin-bottom: 25px;
        }

        .method-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .method-features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .method-features li i {
            color: #28a745;
            font-size: 12px;
        }

        .method-action {
            margin-top: auto;
        }

        .security-notice {
            margin-bottom: 30px;
        }

        .notice-content {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 1px solid #c3e6cb;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .notice-content i {
            font-size: 2rem;
            color: #28a745;
        }

        .notice-content h4 {
            color: #155724;
            margin: 0 0 5px 0;
        }

        .notice-content p {
            color: #155724;
            margin: 0;
            font-size: 14px;
        }

        .page-actions {
            text-align: center;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            width: 100%;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* Loading Overlay */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4682B4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Card Payment Styles */
        .supported-cards {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .card-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .card-logo:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }

        .visa-logo i {
            color: #1A1F71;
            font-size: 1.5rem;
        }

        .mastercard-logo i {
            color: #EB001B;
            font-size: 1.5rem;
        }

        .meeza-logo i {
            color: #00A651;
            font-size: 1.5rem;
        }

        .card-logo span {
            font-size: 11px;
            font-weight: 600;
            color: #333;
        }

        .card-types {
            margin-top: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .card-type {
            font-size: 13px;
            color: #555;
            text-align: right;
        }

        .supported-banks {
            margin-top: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .bank-name {
            font-size: 12px;
            color: #007bff;
            text-align: right;
            padding: 2px 8px;
            background: rgba(0, 123, 255, 0.1);
            border-radius: 10px;
            margin: 2px 0;
            display: inline-block;
        }

        .security-badges {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .security-badge {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 6px 12px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .security-badge i {
            font-size: 12px;
        }

        .card-payment-btn {
            position: relative;
            overflow: hidden;
        }

        .card-payment-btn small {
            display: block;
            font-size: 11px;
            opacity: 0.8;
            margin-top: 2px;
        }

        .card-payment-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .card-payment-btn:hover::before {
            left: 100%;
        }

        /* Enhanced Security Notice Styles */
        .security-notice-section {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            border: 2px solid rgba(40, 167, 69, 0.2);
        }

        .security-notice-content {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .security-notice-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .security-notice-text h3 {
            color: #28a745;
            margin: 0 0 20px 0;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .security-features {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .security-feature {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 10px 0;
        }

        .security-feature i {
            color: #28a745;
            font-size: 16px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .security-feature span {
            color: #333;
            font-size: 14px;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .payment-grid {
                grid-template-columns: 1fr;
            }

            .summary-header {
                flex-direction: column;
                text-align: center;
            }

            .summary-price {
                justify-content: center;
            }

            .notice-content {
                flex-direction: column;
                text-align: center;
            }

            .supported-cards {
                gap: 10px;
            }

            .card-logo {
                padding: 6px 8px;
            }

            .security-badges {
                gap: 8px;
            }

            .security-badge {
                padding: 4px 8px;
                font-size: 10px;
            }
        }
    </style>


    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
