<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Get user's course status
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);

// Check if pending subscription is old (more than 1 hour) and remove it
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'pending') {
    $createdAt = new DateTime($userCourseStatus['created_at']);
    $now = new DateTime();
    $hoursDiff = $now->diff($createdAt)->h + ($now->diff($createdAt)->days * 24);

    if ($hoursDiff >= 1) {
        // Remove old pending subscription
        $courseManager->removePendingSubscription($userId, $courseId);
        $userCourseStatus = null; // Reset status
    }
}

$hasAccess = $userCourseStatus && $userCourseStatus['activation_status'] === 'active';
$isPending = $userCourseStatus && $userCourseStatus['activation_status'] === 'pending';

// Handle cancel pending request
if (isset($_POST['cancel_pending']) && $isPending) {
    $courseManager->removePendingSubscription($userId, $courseId);
    header('Location: ' . SITE_URL . '/page/course_details.php?id=' . $courseId . '&message=cancelled');
    exit;
}

// Parse modal images
$modalImages = [];
if ($course['modal_images']) {
    $modalImages = json_decode($course['modal_images'], true) ?: [];
}

// Parse features
$features = [];
if ($course['features']) {
    $features = array_filter(array_map('trim', explode("\n", $course['features'])));
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="<?php echo SITE_URL; ?>/page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="<?php echo SITE_URL; ?>/page/courses.php">الكورسات</a>
                    <span>/</span>
                    <span><?php echo htmlspecialchars($course['title']); ?></span>
                </div>

                <div class="course-details-container">
                    <!-- Course Header -->
                    <div class="course-header-section">
                        <div class="course-main-info">
                            <div class="course-image-section">
                                <?php if ($course['main_image']): ?>
                                    <img src="<?php echo SITE_URL; ?>/uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($course['title']); ?>" 
                                         class="main-course-image">
                                <?php else: ?>
                                    <div class="course-placeholder-large">
                                        <span class="course-icon-large">📚</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <div class="discount-badge-large">
                                        خصم <?php echo $course['discount_percentage']; ?>%
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="course-info-section">
                                <h1 class="course-title-large"><?php echo htmlspecialchars($course['title']); ?></h1>
                                <p class="course-subject-large"><?php echo htmlspecialchars($course['subject']); ?></p>
                                
                                <div class="course-price-section">
                                    <?php if ($course['discount_percentage'] > 0): ?>
                                        <span class="original-price-large"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                        <span class="discounted-price-large"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                    <?php else: ?>
                                        <span class="current-price-large"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <?php endif; ?>
                                </div>

                                <div class="course-actions-section">
                                    <?php if ($hasAccess): ?>
                                        <a href="<?php echo SITE_URL; ?>/page/course_content.php?id=<?php echo $course['id']; ?>" class="btn btn-success btn-large">
                                            <span class="btn-icon">🎓</span>
                                            دخول الكورس
                                        </a>
                                    <?php elseif ($isPending): ?>
                                        <div class="pending-status-card">
                                            <div class="pending-icon">⏳</div>
                                            <div class="pending-info">
                                                <h3>طلبك قيد المراجعة</h3>
                                                <p>سيتم تفعيل الكورس خلال 1-24 ساعة من تقديم الطلب</p>
                                            </div>
                                        </div>
                                        <div class="pending-actions">
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من إلغاء طلب التفعيل؟')">
                                                <button type="submit" name="cancel_pending" class="btn btn-danger btn-large">
                                                    <span class="btn-icon">❌</span>
                                                    إلغاء الطلب
                                                </button>
                                            </form>
                                            <a href="<?php echo SITE_URL; ?>/page/courses.php" class="btn btn-secondary btn-large">
                                                <span class="btn-icon">📚</span>
                                                تصفح كورسات أخرى
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <a href="<?php echo SITE_URL; ?>/page/course_register.php?id=<?php echo $course['id']; ?>" class="btn btn-primary btn-large">
                                            <span class="btn-icon">📝</span>
                                            سجل الآن
                                        </a>
                                        <a href="<?php echo SITE_URL; ?>/page/courses.php" class="btn btn-secondary btn-large">
                                            <span class="btn-icon">📚</span>
                                            تصفح كورسات أخرى
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Description -->
                    <div class="course-section">
                        <h2 class="section-title">وصف الكورس</h2>
                        <div class="course-description-content">
                            <p><?php echo nl2br(htmlspecialchars($course['description'])); ?></p>
                        </div>
                    </div>

                    <!-- Course Features -->
                    <?php if (!empty($features)): ?>
                        <div class="course-section">
                            <h2 class="section-title">مميزات الكورس</h2>
                            <div class="course-features-grid">
                                <?php foreach ($features as $feature): ?>
                                    <div class="feature-item">
                                        <span class="feature-icon">✓</span>
                                        <span class="feature-text"><?php echo htmlspecialchars($feature); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Additional Images -->
                    <?php if (!empty($modalImages)): ?>
                        <div class="course-section">
                            <h2 class="section-title">صور إضافية</h2>
                            <div class="additional-images-grid">
                                <?php foreach ($modalImages as $image): ?>
                                    <div class="additional-image-item">
                                        <img src="<?php echo SITE_URL; ?>/uploads/courses/<?php echo htmlspecialchars($image); ?>"
                                             alt="صورة إضافية للكورس"
                                             onclick="openImageModal(this.src)">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Course Information -->
                    <div class="course-section">
                        <h2 class="section-title">معلومات الكورس</h2>
                        <div class="course-info-grid">
                            <div class="info-card">
                                <div class="info-icon">🎓</div>
                                <div class="info-content">
                                    <h4>المرحلة التعليمية</h4>
                                    <p>
                                        <?php 
                                        $levels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي', 'all' => 'جميع المراحل'];
                                        echo $levels[$course['education_level']] ?? $course['education_level'];
                                        ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-icon">📚</div>
                                <div class="info-content">
                                    <h4>نوع التعليم</h4>
                                    <p>
                                        <?php 
                                        $types = ['azhari' => 'أزهري', 'general' => 'عام', 'all' => 'جميع الأنواع'];
                                        echo $types[$course['education_type']] ?? $course['education_type'];
                                        ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-icon">📖</div>
                                <div class="info-content">
                                    <h4>الصف الدراسي</h4>
                                    <p>
                                        <?php 
                                        if ($course['grade'] === 'all') {
                                            echo 'جميع الصفوف';
                                        } else {
                                            $grades = ['1' => 'الأول', '2' => 'الثاني', '3' => 'الثالث', '4' => 'الرابع', '5' => 'الخامس', '6' => 'السادس'];
                                            echo 'الصف ' . ($grades[$course['grade']] ?? $course['grade']);
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="info-card">
                                <div class="info-icon">🔬</div>
                                <div class="info-content">
                                    <h4>التخصص</h4>
                                    <p>
                                        <?php 
                                        $specializations = ['scientific' => 'علمي', 'literary' => 'أدبي', 'all' => 'جميع التخصصات'];
                                        echo $specializations[$course['specialization']] ?? $course['specialization'];
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="image-modal-content">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" src="" alt="صورة مكبرة">
        </div>
    </div>

    <style>
        .course-details-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }

        .course-header-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .course-main-info {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 40px;
            align-items: start;
        }

        .course-image-section {
            position: relative;
        }

        .main-course-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .course-placeholder-large {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .course-icon-large {
            font-size: 80px;
            color: white;
        }

        .discount-badge-large {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .course-info-section {
            padding: 20px 0;
        }

        .course-title-large {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .course-subject-large {
            font-size: 20px;
            color: #4682B4;
            font-weight: 600;
            background: rgba(135, 206, 235, 0.1);
            padding: 8px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 25px;
        }

        .course-price-section {
            margin-bottom: 30px;
        }

        .original-price-large {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 20px;
            margin-left: 15px;
        }

        .discounted-price-large {
            color: #dc3545;
            font-size: 32px;
            font-weight: 700;
        }

        .current-price-large {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
        }

        .course-actions-section {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 18px 40px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .pending-status-card {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .pending-icon {
            font-size: 48px;
            flex-shrink: 0;
        }

        .pending-info h3 {
            color: #856404;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .pending-info p {
            color: #856404;
            font-size: 16px;
            margin: 0;
            line-height: 1.5;
        }

        .pending-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
        }

        .course-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #87CEEB;
            display: inline-block;
        }

        .course-description-content {
            font-size: 16px;
            line-height: 1.8;
            color: #495057;
        }

        .course-features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #28a745;
        }

        .feature-icon {
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
        }

        .feature-text {
            color: #2c3e50;
            font-size: 16px;
        }

        .additional-images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .additional-image-item {
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .additional-image-item:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .additional-image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .course-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            border-color: #87CEEB;
            transform: translateY(-5px);
        }

        .info-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .info-card h4 {
            color: #4682B4;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .info-card p {
            color: #6c757d;
            font-size: 16px;
            margin: 0;
        }

        /* Image Modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .image-modal-content {
            position: relative;
            margin: auto;
            display: block;
            width: 90%;
            max-width: 800px;
            top: 50%;
            transform: translateY(-50%);
        }

        .image-modal-content img {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .image-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-modal-close:hover {
            color: #ccc;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .course-main-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .course-title-large {
                font-size: 28px;
            }

            .course-subject-large {
                font-size: 16px;
            }

            .discounted-price-large,
            .current-price-large {
                font-size: 24px;
            }

            .course-actions-section {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }

            .course-features-grid {
                grid-template-columns: 1fr;
            }

            .course-info-grid {
                grid-template-columns: 1fr;
            }

            .additional-images-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = src;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
