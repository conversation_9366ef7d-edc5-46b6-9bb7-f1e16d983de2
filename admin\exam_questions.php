<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get exam ID
$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;

if (!$exam_id) {
    header('Location: lessons.php');
    exit;
}

// Get exam info
$stmt = $db->prepare("
    SELECT le.*, l.title as lesson_title, cs.name as subject_name, cs.color 
    FROM lesson_exams le 
    JOIN lessons l ON le.lesson_id = l.id
    JOIN curriculum_subjects cs ON l.subject_id = cs.id 
    WHERE le.id = ?
");
$stmt->execute([$exam_id]);
$exam = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exam) {
    header('Location: lessons.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_question':
                $question_text = $_POST['question_text'];
                $question_type = $_POST['question_type'];
                $correct_answer = $_POST['correct_answer'];
                $explanation = $_POST['explanation'];
                $points = $_POST['points'];
                $question_order = $_POST['question_order'];
                
                $options = null;
                if ($question_type === 'multiple_choice') {
                    $options = json_encode([
                        'option_a' => $_POST['option_a'],
                        'option_b' => $_POST['option_b'],
                        'option_c' => $_POST['option_c'],
                        'option_d' => $_POST['option_d']
                    ]);
                }
                
                $stmt = $db->prepare("INSERT INTO exam_questions (exam_id, question_text, question_type, options, correct_answer, explanation, points, question_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$exam_id, $question_text, $question_type, $options, $correct_answer, $explanation, $points, $question_order]);
                
                $success = "تم إضافة السؤال بنجاح";
                break;
                
            case 'delete_question':
                $question_id = $_POST['question_id'];
                $stmt = $db->prepare("DELETE FROM exam_questions WHERE id = ?");
                $stmt->execute([$question_id]);
                
                $success = "تم حذف السؤال بنجاح";
                break;
        }
    }
}

// Get questions
$questions_stmt = $db->prepare("SELECT * FROM exam_questions WHERE exam_id = ? ORDER BY question_order, id");
$questions_stmt->execute([$exam_id]);
$questions = $questions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total points
$total_points = array_sum(array_column($questions, 'points'));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أسئلة الامتحان - <?php echo htmlspecialchars($exam['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <?php include __DIR__ . '/../includes/admin_header.php'; ?>
        
        <div class="admin-container">
            <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            
            <main class="admin-main">
                <div class="admin-header">
                    <div class="header-content">
                        <div class="exam-info">
                            <h1><i class="fas fa-clipboard-check"></i> أسئلة الامتحان</h1>
                            <div class="exam-details">
                                <h2><?php echo htmlspecialchars($exam['title']); ?></h2>
                                <span class="subject-badge" style="background-color: <?php echo $exam['color']; ?>20; color: <?php echo $exam['color']; ?>;">
                                    <?php echo htmlspecialchars($exam['subject_name']); ?>
                                </span>
                                <span class="lesson-badge"><?php echo htmlspecialchars($exam['lesson_title']); ?></span>
                                <span class="exam-stats">
                                    <i class="fas fa-clock"></i>
                                    <?php echo $exam['duration_minutes']; ?> دقيقة
                                </span>
                                <span class="exam-stats">
                                    <i class="fas fa-percentage"></i>
                                    60% للنجاح
                                </span>
                            </div>
                        </div>
                        <div class="header-actions">
                            <a href="lesson_content.php?lesson_id=<?php echo $exam['lesson_id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للدرس
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <!-- Exam Statistics -->
                <div class="exam-stats-card">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo count($questions); ?></span>
                            <span class="stat-label">سؤال</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo $total_points; ?></span>
                            <span class="stat-label">نقطة إجمالية</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number"><?php echo count($questions) > 0 ? round($total_points / count($questions), 1) : 0; ?></span>
                            <span class="stat-label">متوسط النقاط</span>
                        </div>
                    </div>
                </div>

                <!-- Add Question Form -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus-circle"></i> إضافة سؤال جديد</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="admin-form">
                            <input type="hidden" name="action" value="add_question">

                            <div class="form-group">
                                <label for="question_text">نص السؤال</label>
                                <textarea name="question_text" id="question_text" class="form-control" rows="3" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="question_type">نوع السؤال</label>
                                    <select name="question_type" id="question_type" class="form-control" required onchange="toggleOptions()">
                                        <option value="">اختر نوع السؤال</option>
                                        <option value="true_false">صح وخطأ</option>
                                        <option value="multiple_choice">اختيار متعدد</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="points">نقاط السؤال</label>
                                    <input type="number" name="points" id="points" class="form-control" min="0.1" step="0.1" value="1" required>
                                </div>
                            </div>

                            <!-- Multiple Choice Options -->
                            <div id="multiple_choice_options" style="display: none;">
                                <h4>خيارات الاختيار المتعدد</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="option_a">الخيار أ</label>
                                        <input type="text" name="option_a" id="option_a" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label for="option_b">الخيار ب</label>
                                        <input type="text" name="option_b" id="option_b" class="form-control">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="option_c">الخيار ج</label>
                                        <input type="text" name="option_c" id="option_c" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label for="option_d">الخيار د</label>
                                        <input type="text" name="option_d" id="option_d" class="form-control">
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="correct_answer">الإجابة الصحيحة</label>
                                    <select name="correct_answer" id="correct_answer" class="form-control" required>
                                        <option value="">اختر الإجابة الصحيحة</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="question_order">ترتيب السؤال</label>
                                    <input type="number" name="question_order" id="question_order" class="form-control" min="0" value="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="explanation">شرح الإجابة (اختياري)</label>
                                <textarea name="explanation" id="explanation" class="form-control" rows="2"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    إضافة السؤال
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Questions List -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> قائمة الأسئلة</h3>
                        <div class="card-actions">
                            <span class="badge badge-primary"><?php echo count($questions); ?> سؤال</span>
                            <span class="badge badge-success"><?php echo $total_points; ?> نقطة</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($questions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-clipboard-check"></i>
                                <h3>لا توجد أسئلة</h3>
                                <p>لم يتم إضافة أي أسئلة لهذا الامتحان بعد</p>
                            </div>
                        <?php else: ?>
                            <div class="questions-list">
                                <?php foreach ($questions as $index => $question): ?>
                                    <div class="question-card exam-question">
                                        <div class="question-header">
                                            <div class="question-number">
                                                <span><?php echo $index + 1; ?></span>
                                            </div>
                                            <div class="question-meta">
                                                <div class="question-type">
                                                    <?php if ($question['question_type'] === 'true_false'): ?>
                                                        <span class="badge badge-info">صح وخطأ</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">اختيار متعدد</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="question-points">
                                                    <span class="points-badge"><?php echo $question['points']; ?> نقطة</span>
                                                </div>
                                            </div>
                                            <div class="question-actions">
                                                <button onclick="deleteQuestion(<?php echo $question['id']; ?>)" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="question-content">
                                            <h4><?php echo htmlspecialchars($question['question_text']); ?></h4>
                                            
                                            <?php if ($question['question_type'] === 'multiple_choice' && $question['options']): ?>
                                                <?php $options = json_decode($question['options'], true); ?>
                                                <div class="question-options">
                                                    <?php foreach ($options as $key => $option): ?>
                                                        <?php if (!empty($option)): ?>
                                                            <div class="option-item <?php echo $question['correct_answer'] === $key ? 'correct' : ''; ?>">
                                                                <span class="option-label"><?php echo strtoupper(substr($key, -1)); ?>)</span>
                                                                <span class="option-text"><?php echo htmlspecialchars($option); ?></span>
                                                                <?php if ($question['correct_answer'] === $key): ?>
                                                                    <i class="fas fa-check-circle correct-icon"></i>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="true-false-answer">
                                                    <span class="answer-label">الإجابة الصحيحة:</span>
                                                    <span class="answer-value <?php echo $question['correct_answer'] === 'true' ? 'true' : 'false'; ?>">
                                                        <?php echo $question['correct_answer'] === 'true' ? 'صح' : 'خطأ'; ?>
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($question['explanation']): ?>
                                                <div class="question-explanation">
                                                    <strong>شرح الإجابة:</strong>
                                                    <p><?php echo htmlspecialchars($question['explanation']); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <h3>تأكيد الحذف</h3>
            <p>هل أنت متأكد من حذف هذا السؤال؟</p>
            <div class="modal-actions">
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="action" value="delete_question">
                    <input type="hidden" name="question_id" id="deleteQuestionId">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">إلغاء</button>
                </form>
            </div>
        </div>
    </div>

    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .admin-container {
            display: flex;
            flex: 1;
        }

        .admin-main {
            flex: 1;
            padding: 20px;
            margin-left: 280px;
        }

        .admin-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #dc3545;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .exam-info h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .exam-details {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .exam-details h2 {
            color: #dc3545;
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .subject-badge, .lesson-badge, .exam-stats {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .exam-stats {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .exam-stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
            border-left: 5px solid #dc3545;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 15px;
            text-align: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #dc3545;
            line-height: 1;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .card-actions {
            display: flex;
            gap: 10px;
        }

        .card-body {
            padding: 30px;
        }

        .admin-form {
            max-width: 800px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .questions-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .question-card {
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .question-card.exam-question {
            border-left: 4px solid #dc3545;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .question-header {
            background: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
        }

        .question-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .question-meta {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-type .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .badge-info {
            background: #17a2b8;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-primary {
            background: #007bff;
            color: white;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .points-badge {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .question-content {
            padding: 20px;
        }

        .question-content h4 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.5;
        }

        .question-options {
            margin: 15px 0;
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            margin: 5px 0;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .option-item.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .option-label {
            font-weight: bold;
            color: #dc3545;
            min-width: 20px;
        }

        .option-text {
            flex: 1;
            color: #2c3e50;
        }

        .correct-icon {
            color: #28a745;
            font-size: 16px;
        }

        .true-false-answer {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .answer-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .answer-value {
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
        }

        .answer-value.true {
            background: #28a745;
            color: white;
        }

        .answer-value.false {
            background: #dc3545;
            color: white;
        }

        .question-explanation {
            margin-top: 15px;
            padding: 15px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }

        .question-explanation strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }

        .question-explanation p {
            color: #6c757d;
            margin: 0;
            line-height: 1.5;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .modal-content h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .modal-content p {
            color: #6c757d;
            margin-bottom: 25px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
                padding: 10px;
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .exam-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .exam-stats-card {
                flex-direction: column;
                gap: 20px;
            }

            .stat-item {
                justify-content: center;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .question-meta {
                flex-direction: column;
                gap: 5px;
            }

            .modal-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>

    <script>
        function toggleOptions() {
            const questionType = document.getElementById('question_type').value;
            const optionsDiv = document.getElementById('multiple_choice_options');
            const correctAnswerSelect = document.getElementById('correct_answer');

            // Clear previous options
            correctAnswerSelect.innerHTML = '<option value="">اختر الإجابة الصحيحة</option>';

            if (questionType === 'multiple_choice') {
                optionsDiv.style.display = 'block';
                correctAnswerSelect.innerHTML = `
                    <option value="">اختر الإجابة الصحيحة</option>
                    <option value="option_a">الخيار أ</option>
                    <option value="option_b">الخيار ب</option>
                    <option value="option_c">الخيار ج</option>
                    <option value="option_d">الخيار د</option>
                `;
            } else if (questionType === 'true_false') {
                optionsDiv.style.display = 'none';
                correctAnswerSelect.innerHTML = `
                    <option value="">اختر الإجابة الصحيحة</option>
                    <option value="true">صح</option>
                    <option value="false">خطأ</option>
                `;
            } else {
                optionsDiv.style.display = 'none';
            }
        }

        function deleteQuestion(questionId) {
            document.getElementById('deleteQuestionId').value = questionId;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
