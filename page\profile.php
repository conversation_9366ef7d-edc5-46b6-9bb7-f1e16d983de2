<?php
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$userManager = new UserManager();
$userData = $userManager->getUserById($_SESSION['user_id']);
$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $updateData = [
            'first_name' => trim($_POST['first_name'] ?? ''),
            'second_name' => trim($_POST['second_name'] ?? ''),
            'third_name' => trim($_POST['third_name'] ?? ''),
            'fourth_name' => trim($_POST['fourth_name'] ?? ''),
            'personal_phone' => trim($_POST['personal_phone'] ?? ''),
            'father_phone' => trim($_POST['father_phone'] ?? ''),
            'mother_phone' => trim($_POST['mother_phone'] ?? ''),
            'education_level' => $_POST['education_level'] ?? '',
            'education_type' => $_POST['education_type'] ?? '',
            'grade' => $_POST['grade'] ?? '',
            'specialization' => $_POST['specialization'] ?? null
        ];

        // Validate required fields
        $requiredFields = ['first_name', 'second_name', 'third_name', 'fourth_name', 'personal_phone', 'father_phone', 'mother_phone', 'education_level', 'education_type', 'grade'];
        
        foreach ($requiredFields as $field) {
            if (empty($updateData[$field])) {
                throw new Exception('جميع الحقول مطلوبة');
            }
        }

        // Update user data
        $result = $userManager->updateUserProfile($_SESSION['user_id'], $updateData);
        
        if ($result) {
            $message = 'تم تحديث الملف الشخصي بنجاح';
            // Refresh user data
            $userData = $userManager->getUserById($_SESSION['user_id']);
            // Update session data
            $_SESSION['first_name'] = $updateData['first_name'];
        } else {
            throw new Exception('فشل في تحديث الملف الشخصي');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الملف الشخصي - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="profile-container">
                <div class="profile-header">
                    <h1>تعديل الملف الشخصي</h1>
                    <p>يمكنك تحديث بياناتك الشخصية من هنا</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <form method="POST" class="profile-form">
                    <div class="form-section">
                        <h3>البيانات الشخصية</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name" class="form-label">الاسم الأول *</label>
                                <input type="text" id="first_name" name="first_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['first_name'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="second_name" class="form-label">الاسم الثاني *</label>
                                <input type="text" id="second_name" name="second_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['second_name'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="third_name" class="form-label">الاسم الثالث *</label>
                                <input type="text" id="third_name" name="third_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['third_name'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="fourth_name" class="form-label">الاسم الرابع *</label>
                                <input type="text" id="fourth_name" name="fourth_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['fourth_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>بيانات الاتصال</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="personal_phone" class="form-label">الهاتف الشخصي *</label>
                                <input type="tel" id="personal_phone" name="personal_phone" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['personal_phone'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="father_phone" class="form-label">هاتف الأب *</label>
                                <input type="tel" id="father_phone" name="father_phone" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['father_phone'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="mother_phone" class="form-label">هاتف الأم *</label>
                                <input type="tel" id="mother_phone" name="mother_phone" class="form-input" 
                                       value="<?php echo htmlspecialchars($userData['mother_phone'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>البيانات التعليمية</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="education_level" class="form-label">المرحلة التعليمية *</label>
                                <select id="education_level" name="education_level" class="form-select" required>
                                    <option value="">اختر المرحلة</option>
                                    <option value="primary" <?php echo ($userData['education_level'] ?? '') === 'primary' ? 'selected' : ''; ?>>ابتدائي</option>
                                    <option value="preparatory" <?php echo ($userData['education_level'] ?? '') === 'preparatory' ? 'selected' : ''; ?>>إعدادي</option>
                                    <option value="secondary" <?php echo ($userData['education_level'] ?? '') === 'secondary' ? 'selected' : ''; ?>>ثانوي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="education_type" class="form-label">نوع التعليم *</label>
                                <select id="education_type" name="education_type" class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="azhari" <?php echo ($userData['education_type'] ?? '') === 'azhari' ? 'selected' : ''; ?>>أزهري</option>
                                    <option value="general" <?php echo ($userData['education_type'] ?? '') === 'general' ? 'selected' : ''; ?>>عام</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="grade" class="form-label">الصف *</label>
                                <select id="grade" name="grade" class="form-select" required>
                                    <option value="">اختر الصف</option>
                                    <option value="1" <?php echo ($userData['grade'] ?? '') === '1' ? 'selected' : ''; ?>>الأول</option>
                                    <option value="2" <?php echo ($userData['grade'] ?? '') === '2' ? 'selected' : ''; ?>>الثاني</option>
                                    <option value="3" <?php echo ($userData['grade'] ?? '') === '3' ? 'selected' : ''; ?>>الثالث</option>
                                    <option value="4" <?php echo ($userData['grade'] ?? '') === '4' ? 'selected' : ''; ?>>الرابع</option>
                                    <option value="5" <?php echo ($userData['grade'] ?? '') === '5' ? 'selected' : ''; ?>>الخامس</option>
                                    <option value="6" <?php echo ($userData['grade'] ?? '') === '6' ? 'selected' : ''; ?>>السادس</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="specialization" class="form-label">التخصص (للثانوي)</label>
                                <select id="specialization" name="specialization" class="form-select">
                                    <option value="">اختر التخصص</option>
                                    <option value="scientific" <?php echo ($userData['specialization'] ?? '') === 'scientific' ? 'selected' : ''; ?>>علمي</option>
                                    <option value="literary" <?php echo ($userData['specialization'] ?? '') === 'literary' ? 'selected' : ''; ?>>أدبي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        <a href="<?php echo SITE_URL; ?>/page/dashboard.php" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <style>
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .profile-header p {
            color: #666;
            font-size: 16px;
        }

        .profile-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .form-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .form-section:last-of-type {
            border-bottom: none;
        }

        .form-section h3 {
            color: #4682B4;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #4682B4;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .profile-form {
                padding: 20px;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // Show/hide specialization based on education level
        document.getElementById('education_level').addEventListener('change', function() {
            const specializationGroup = document.getElementById('specialization').closest('.form-group');
            if (this.value === 'secondary') {
                specializationGroup.style.display = 'block';
            } else {
                specializationGroup.style.display = 'none';
                document.getElementById('specialization').value = '';
            }
        });

        // Initialize specialization visibility
        document.addEventListener('DOMContentLoaded', function() {
            const educationLevel = document.getElementById('education_level').value;
            const specializationGroup = document.getElementById('specialization').closest('.form-group');
            if (educationLevel !== 'secondary') {
                specializationGroup.style.display = 'none';
            }
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
