<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userManager = new UserManager();
$userData = $userManager->getUserById($_SESSION['user_id']);
$message = '';
$error = '';

// Get user's subscription information
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date, u.current_plan_id,
                         sp.name as plan_name, sp.icon as plan_icon, sp.color as plan_color,
                         sp.price, sp.discounted_price
                         FROM users u
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.id = ?";
    $stmt = $db->prepare($subscriptionQuery);
    $stmt->execute([$_SESSION['user_id']]);
    $userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

    $has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                       $userSubscription['subscription_end_date'] &&
                       strtotime($userSubscription['subscription_end_date']) > time();
} catch (Exception $e) {
    $userSubscription = null;
    $has_subscription = false;
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'change_password') {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new Exception('جميع حقول كلمة المرور مطلوبة');
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new Exception('كلمة المرور الجديدة وتأكيدها غير متطابقين');
            }
            
            if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
                throw new Exception('كلمة المرور يجب أن تكون ' . PASSWORD_MIN_LENGTH . ' أحرف على الأقل');
            }
            
            // Verify current password
            $user = $userManager->authenticateUser($userData['username'], $currentPassword);
            if (!$user) {
                throw new Exception('كلمة المرور الحالية غير صحيحة');
            }
            
            // Update password
            $result = $userManager->updateUserPassword($_SESSION['user_id'], $newPassword);
            if ($result) {
                $message = 'تم تغيير كلمة المرور بنجاح';
            } else {
                throw new Exception('فشل في تغيير كلمة المرور');
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="settings-container">
                <div class="settings-header">
                    <h1>الإعدادات</h1>
                    <p>إدارة إعدادات حسابك وتفضيلاتك</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <div class="settings-grid">
                    <!-- Account Information -->
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>معلومات الحساب</h3>
                            <p>البيانات الأساسية لحسابك</p>
                        </div>
                        <div class="card-content">
                            <div class="info-item">
                                <label>اسم المستخدم:</label>
                                <span><?php echo htmlspecialchars($userData['username']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>البريد الإلكتروني:</label>
                                <span><?php echo htmlspecialchars($userData['email']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>تاريخ التسجيل:</label>
                                <span><?php echo date('Y-m-d', strtotime($userData['created_at'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>آخر تسجيل دخول:</label>
                                <span><?php echo $userData['last_login'] ? date('Y-m-d H:i', strtotime($userData['last_login'])) : 'لم يسجل'; ?></span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="<?php echo SITE_URL; ?>/page/profile.php" class="btn btn-primary">تعديل الملف الشخصي</a>
                        </div>
                    </div>

                    <!-- Subscription Information -->
                    <div class="settings-card subscription-card">
                        <div class="card-header">
                            <h3>معلومات الاشتراك</h3>
                            <p>تفاصيل خطة الاشتراك الحالية</p>
                        </div>
                        <div class="card-content">
                            <?php if ($has_subscription): ?>
                                <div class="subscription-info-section">
                                    <div class="subscription-plan-display">
                                        <div class="plan-icon-display" style="background: linear-gradient(135deg, <?php echo $userSubscription['plan_color']; ?>20, <?php echo $userSubscription['plan_color']; ?>40); color: <?php echo $userSubscription['plan_color']; ?>;">
                                            <?php echo $userSubscription['plan_icon']; ?>
                                        </div>
                                        <div class="plan-details">
                                            <h4><?php echo htmlspecialchars($userSubscription['plan_name']); ?></h4>
                                            <p class="plan-price"><?php echo number_format($userSubscription['discounted_price'], 0); ?> جنيه مصري</p>
                                        </div>
                                        <div class="plan-status active">
                                            <i class="fas fa-check-circle"></i>
                                            <span>نشط</span>
                                        </div>
                                    </div>

                                    <div class="subscription-details">
                                        <div class="detail-item">
                                            <div class="detail-label">
                                                <i class="fas fa-calendar-alt"></i>
                                                تاريخ انتهاء الاشتراك
                                            </div>
                                            <div class="detail-value">
                                                <?php echo date('d/m/Y', strtotime($userSubscription['subscription_end_date'])); ?>
                                            </div>
                                        </div>

                                        <div class="detail-item">
                                            <div class="detail-label">
                                                <i class="fas fa-clock"></i>
                                                الأيام المتبقية
                                            </div>
                                            <div class="detail-value">
                                                <?php
                                                $days_left = ceil((strtotime($userSubscription['subscription_end_date']) - time()) / (60 * 60 * 24));
                                                echo $days_left . ' يوم';
                                                ?>
                                            </div>
                                        </div>

                                        <div class="detail-item">
                                            <div class="detail-label">
                                                <i class="fas fa-credit-card"></i>
                                                المبلغ المدفوع
                                            </div>
                                            <div class="detail-value">
                                                <?php echo number_format($userSubscription['discounted_price'], 0); ?> جنيه
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-actions">
                                    <button type="button" class="btn btn-outline-danger" onclick="showCancelSubscriptionModal()">
                                        <i class="fas fa-times-circle"></i>
                                        إلغاء الاشتراك
                                    </button>
                                    <a href="<?php echo SITE_URL; ?>/page/subscriptions.php" class="btn btn-outline-primary">
                                        <i class="fas fa-upgrade"></i>
                                        ترقية الخطة
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="no-subscription">
                                    <div class="no-subscription-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <h4>لا يوجد اشتراك نشط</h4>
                                    <p>اشترك الآن للوصول لجميع المحتويات التعليمية والمميزات الحصرية</p>
                                    <div class="card-actions">
                                        <a href="<?php echo SITE_URL; ?>/page/subscriptions.php" class="btn btn-primary">
                                            <i class="fas fa-crown"></i>
                                            اشترك الآن
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Password Change -->
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>تغيير كلمة المرور</h3>
                            <p>قم بتحديث كلمة المرور لحماية حسابك</p>
                        </div>
                        <form method="POST" class="password-form">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="form-group">
                                <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                                <input type="password" id="current_password" name="current_password" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                                <input type="password" id="new_password" name="new_password" class="form-input" required>
                                <small class="form-help">يجب أن تكون <?php echo PASSWORD_MIN_LENGTH; ?> أحرف على الأقل</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" id="confirm_password" name="confirm_password" class="form-input" required>
                            </div>
                            
                            <div class="card-actions">
                                <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                            </div>
                        </form>
                    </div>

                    <!-- Privacy Settings -->
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>إعدادات الخصوصية</h3>
                            <p>تحكم في خصوصية بياناتك</p>
                        </div>
                        <div class="card-content">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>إشعارات البريد الإلكتروني</h4>
                                    <p>تلقي إشعارات عبر البريد الإلكتروني</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>إشعارات المتصفح</h4>
                                    <p>تلقي إشعارات في المتصفح</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Actions -->
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>إجراءات الحساب</h3>
                            <p>إجراءات متقدمة للحساب</p>
                        </div>
                        <div class="card-content">
                            <div class="action-item">
                                <div class="action-info">
                                    <h4>تصدير البيانات</h4>
                                    <p>تحميل نسخة من بياناتك</p>
                                </div>
                                <button class="btn btn-secondary">تصدير</button>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-info">
                                    <h4>إلغاء تفعيل الحساب</h4>
                                    <p>إيقاف الحساب مؤقتاً</p>
                                </div>
                                <button class="btn btn-warning">إلغاء التفعيل</button>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-info">
                                    <h4>حذف الحساب</h4>
                                    <p>حذف الحساب نهائياً (لا يمكن التراجع)</p>
                                </div>
                                <button class="btn btn-danger" onclick="confirmDeleteAccount()">حذف الحساب</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .settings-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .settings-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .settings-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .settings-header p {
            color: #666;
            font-size: 16px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .card-header h3 {
            color: #333;
            margin-bottom: 8px;
            font-size: 18px;
        }

        .card-header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }

        .card-content {
            padding: 25px;
        }

        .card-actions {
            padding: 20px 25px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item label {
            font-weight: 500;
            color: #333;
        }

        .info-item span {
            color: #666;
        }

        .setting-item,
        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .setting-item:last-child,
        .action-item:last-child {
            border-bottom: none;
        }

        .setting-info h4,
        .action-info h4 {
            margin: 0 0 5px;
            color: #333;
            font-size: 16px;
        }

        .setting-info p,
        .action-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4682B4;
        }

        .form-help {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        /* Toggle Switch */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4682B4;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Subscription Card Styles */
        .subscription-card {
            border-left: 4px solid #4682B4;
        }

        .subscription-info-section {
            margin-bottom: 25px;
        }

        .subscription-plan-display {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(70, 130, 180, 0.05), rgba(255, 255, 255, 0.95));
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .plan-icon-display {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .plan-details {
            flex: 1;
        }

        .plan-details h4 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .plan-price {
            margin: 0;
            color: #4682B4;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .plan-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .plan-status.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .subscription-details {
            display: grid;
            gap: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #4682B4;
        }

        .detail-label {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            font-weight: 500;
        }

        .detail-label i {
            color: #4682B4;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .no-subscription {
            text-align: center;
            padding: 40px 20px;
        }

        .no-subscription-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
        }

        .no-subscription h4 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }

        .no-subscription p {
            color: #666;
            margin: 0 0 25px 0;
            line-height: 1.6;
        }

        .btn-outline-primary {
            background: transparent;
            border: 2px solid #4682B4;
            color: #4682B4;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline-primary:hover {
            background: #4682B4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
        }

        .btn-outline-danger {
            background: transparent;
            border: 2px solid #dc3545;
            color: #dc3545;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline-danger:hover {
            background: #dc3545;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
        }

        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }

            .card-header,
            .card-content,
            .card-actions {
                padding: 20px;
            }

            .setting-item,
            .action-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .subscription-plan-display {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .detail-item {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // Confirm password match
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // Confirm account deletion
        function confirmDeleteAccount() {
            customConfirm.show(
                'حذف الحساب',
                'هل أنت متأكد من حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.'
            ).then(confirmed => {
                if (confirmed) {
                    customConfirm.show(
                        'تأكيد أخير',
                        'سيتم حذف جميع بياناتك نهائياً. هل تريد المتابعة؟',
                        {
                            icon: '🚨',
                            confirmText: 'حذف نهائي',
                            cancelText: 'إلغاء'
                        }
                    ).then(finalConfirmed => {
                        if (finalConfirmed) {
                            // Here you would implement the account deletion logic
                            alert('سيتم تنفيذ هذه الميزة قريباً');
                        }
                    });
                }
            });
        }

        // Cancel Subscription Modal Functions
        function showCancelSubscriptionModal() {
            const modal = document.createElement('div');
            modal.className = 'subscription-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        padding: 25px;
                        border-bottom: 1px solid #eee;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h3 style="margin: 0; color: #333;">إلغاء الاشتراك</h3>
                        <span onclick="closeCancelModal()" style="
                            cursor: pointer;
                            font-size: 24px;
                            color: #999;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='transparent'">&times;</span>
                    </div>
                    <div style="padding: 25px;">
                        <div style="text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ffc107; margin-bottom: 20px;"></i>
                            <h4 style="margin: 0 0 20px 0; color: #333;">هل أنت متأكد من إلغاء الاشتراك؟</h4>
                            <p style="margin: 20px 0; line-height: 1.6; color: #666;">
                                <strong style="color: #dc3545;">⚠️ تنبيه مهم:</strong><br>
                                • سيتم إيقاف الوصول للمحتوى التعليمي فوراً<br>
                                • للحصول على استرداد جزئي، تواصل معنا على:<br>
                                <strong style="color: #28a745; font-size: 18px;">📱 01126130559</strong><br>
                                • سيتم خصم قيمة الأيام المستخدمة من المبلغ المسترد<br>
                                • هذا الإجراء لا يمكن التراجع عنه
                            </p>
                        </div>
                    </div>
                    <div style="
                        padding: 20px 25px;
                        border-top: 1px solid #eee;
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                        background: #f8f9fa;
                        border-radius: 0 0 15px 15px;
                    ">
                        <button onclick="closeCancelModal()" style="
                            padding: 12px 20px;
                            border: 2px solid #6c757d;
                            background: transparent;
                            color: #6c757d;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        " onmouseover="this.style.background='#6c757d'; this.style.color='white';" onmouseout="this.style.background='transparent'; this.style.color='#6c757d';">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                        <button onclick="confirmCancelSubscription()" style="
                            padding: 12px 20px;
                            border: none;
                            background: linear-gradient(135deg, #dc3545, #c82333);
                            color: white;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(220, 53, 69, 0.4)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                            <i class="fas fa-check"></i>
                            تأكيد الإلغاء
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function closeCancelModal() {
            const modal = document.querySelector('.subscription-modal');
            if (modal) {
                modal.remove();
            }
        }

        function reloadPageAfterCancel() {
            // Close modal first
            closeCancelModal();

            // Clear any cached data
            if (typeof(Storage) !== "undefined") {
                localStorage.removeItem('user_subscription_status');
                sessionStorage.removeItem('user_subscription_status');
            }

            // Force reload the page
            window.location.reload(true);
        }

        function confirmCancelSubscription() {
            // Show loading
            const modal = document.querySelector('.subscription-modal div:nth-child(1) div:nth-child(2)');
            modal.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #4682B4; margin-bottom: 20px;"></i>
                    <h4 style="margin: 0 0 10px 0; color: #333;">جاري إلغاء الاشتراك...</h4>
                    <p style="margin: 0; color: #666;">يرجى الانتظار</p>
                </div>
            `;

            fetch('cancel_subscription.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    modal.innerHTML = `
                        <div style="text-align: center;">
                            <i class="fas fa-check-circle" style="font-size: 3rem; color: #28a745; margin-bottom: 20px;"></i>
                            <h4 style="margin: 0 0 20px 0; color: #333;">تم إلغاء الاشتراك بنجاح</h4>
                            <p style="margin: 0 0 15px 0; color: #666;">تم إلغاء اشتراكك في خطة: ${data.plan_name}</p>
                            <p style="margin: 0; color: #666;">
                                <strong>للحصول على استرداد جزئي:</strong><br>
                                تواصل معنا على: <strong style="color: #28a745;">01126130559</strong>
                            </p>
                        </div>
                    `;

                    // Update modal actions
                    const actions = document.querySelector('.subscription-modal div:nth-child(1) div:nth-child(3)');
                    actions.innerHTML = `
                        <button onclick="reloadPageAfterCancel()" style="
                            padding: 12px 20px;
                            border: none;
                            background: linear-gradient(135deg, #4682B4, #20B2AA);
                            color: white;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        ">
                            <i class="fas fa-refresh"></i>
                            تحديث الصفحة
                        </button>
                    `;

                    // Auto reload after 3 seconds
                    setTimeout(() => {
                        reloadPageAfterCancel();
                    }, 3000);

                } else {
                    modal.innerHTML = `
                        <div style="text-align: center;">
                            <i class="fas fa-times-circle" style="font-size: 3rem; color: #dc3545; margin-bottom: 20px;"></i>
                            <h4 style="margin: 0 0 20px 0; color: #333;">فشل في إلغاء الاشتراك</h4>
                            <p style="margin: 0 0 15px 0; color: #666;">${data.message}</p>
                            <p style="margin: 0; color: #666;">
                                يرجى المحاولة مرة أخرى أو التواصل معنا على:<br>
                                <strong style="color: #28a745;">01126130559</strong>
                            </p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                modal.innerHTML = `
                    <div style="text-align: center;">
                        <i class="fas fa-times-circle" style="font-size: 3rem; color: #dc3545; margin-bottom: 20px;"></i>
                        <h4 style="margin: 0 0 20px 0; color: #333;">خطأ في الاتصال</h4>
                        <p style="margin: 0; color: #666;">
                            حدث خطأ أثناء إلغاء الاشتراك<br>
                            يرجى التواصل معنا على: <strong style="color: #28a745;">01126130559</strong>
                        </p>
                    </div>
                `;
            });
        }
    </script>
    <script src="<?php echo SITE_URL; ?>/js/custom-confirm.js"></script>
</body>
</html>
