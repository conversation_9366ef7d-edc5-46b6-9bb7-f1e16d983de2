<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$courseManager = new CourseManager();
$userManager = new UserManager();

// Get user data for filtering
$userData = $userManager->getUserById($_SESSION['user_id']);

// Get courses based on user's education level
$filters = [];
if ($userData) {
    $filters = [
        'education_level' => $userData['education_level'],
        'education_type' => $userData['education_type'],
        'grade' => $userData['grade'],
        'specialization' => $userData['specialization']
    ];
}

$courses = $courseManager->getAllCourses($filters);

// Get user's course subscriptions
$userCourses = [];
$completedCourses = [];

// Create completion notes table if it doesn't exist
$db = Database::getInstance()->getConnection();
try {
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_completion_notes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            completion_date DATETIME NOT NULL,
            note TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_completion (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (Exception $e) {
    // Table creation failed, continue without completion tracking
    error_log("Failed to create course_completion_notes table: " . $e->getMessage());
}

foreach ($courses as $course) {
    $status = $courseManager->getUserCourseStatus($_SESSION['user_id'], $course['id']);
    $userCourses[$course['id']] = $status;

    // Check if course is completed
    try {
        $stmt = $db->prepare("SELECT id FROM course_completion_notes WHERE user_id = ? AND course_id = ?");
        $stmt->execute([$_SESSION['user_id'], $course['id']]);
        if ($stmt->fetch()) {
            $completedCourses[$course['id']] = true;
        }
    } catch (Exception $e) {
        // Table doesn't exist or query failed, skip completion check
        error_log("Failed to check course completion: " . $e->getMessage());
    }
}

// Calculate statistics
$totalCourses = count($courses);
$activeCourses = count(array_filter($userCourses, function($status) { return $status && $status['activation_status'] === 'active'; }));
$pendingCourses = count(array_filter($userCourses, function($status) { return $status && $status['activation_status'] === 'pending'; }));
$completedCoursesCount = count($completedCourses);
$progressPercentage = $totalCourses > 0 ? round(($completedCoursesCount / $totalCourses) * 100) : 0;

// Additional statistics
$availableCourses = $totalCourses - $activeCourses - $pendingCourses;
$totalSpent = 0;
$totalSavings = 0;

foreach ($courses as $course) {
    $status = $userCourses[$course['id']] ?? null;
    if ($status && $status['activation_status'] === 'active') {
        $totalSpent += $course['discounted_price'] > 0 ? $course['discounted_price'] : $course['price'];
        if ($course['discount_percentage'] > 0) {
            $totalSavings += $course['price'] - $course['discounted_price'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الكورسات - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/enhanced-ui.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/loading-system.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once '../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <?php include __DIR__ . '/../includes/header.php'; ?>
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <div class="page-container">
                <div class="modern-page-header">
                    <div class="header-content">
                        <div class="header-text">
                            <h1 class="gradient-title">
                                <i class="fas fa-graduation-cap"></i>
                                الكورسات المتاحة
                            </h1>
                            <p class="header-subtitle">اكتشف مجموعة متنوعة من الكورسات والدورات التدريبية المصممة خصيصاً لمستواك التعليمي</p>
                        </div>
                        <div class="header-stats">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number"><?php echo $totalCourses; ?></div>
                                    <div class="stat-label">كورس متاح</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon active">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number"><?php echo $activeCourses; ?></div>
                                    <div class="stat-label">كورس مفعل</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon completed">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number"><?php echo $completedCoursesCount; ?></div>
                                    <div class="stat-label">كورس مكتمل</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon progress">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number"><?php echo $progressPercentage; ?>%</div>
                                    <div class="stat-label">نسبة الإنجاز</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Statistics Row -->
                        <div class="additional-stats">
                            <div class="stat-card-small">
                                <i class="fas fa-clock"></i>
                                <span class="stat-text"><?php echo $pendingCourses; ?> كورس في الانتظار</span>
                            </div>
                            <div class="stat-card-small">
                                <i class="fas fa-money-bill-wave"></i>
                                <span class="stat-text"><?php echo number_format($totalSpent); ?> جنيه إجمالي المدفوع</span>
                            </div>
                            <div class="stat-card-small">
                                <i class="fas fa-piggy-bank"></i>
                                <span class="stat-text"><?php echo number_format($totalSavings); ?> جنيه إجمالي الوفورات</span>
                            </div>
                            <div class="stat-card-small">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="stat-text"><?php echo $availableCourses; ?> كورس متاح للشراء</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="search-container">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="courseSearch" placeholder="ابحث عن كورس بالاسم أو الموضوع..." onkeyup="filterCourses()">
                            <button class="clear-search" onclick="clearSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="advanced-filters">
                            <select id="subjectFilter" onchange="filterCourses()">
                                <option value="">جميع المواضيع</option>
                                <?php
                                $subjects = array_unique(array_column($courses, 'subject'));
                                foreach ($subjects as $subject) {
                                    echo '<option value="' . htmlspecialchars($subject) . '">' . htmlspecialchars($subject) . '</option>';
                                }
                                ?>
                            </select>

                            <select id="priceFilter" onchange="filterCourses()">
                                <option value="">جميع الأسعار</option>
                                <option value="free">مجاني</option>
                                <option value="0-100">أقل من 100 جنيه</option>
                                <option value="100-500">100 - 500 جنيه</option>
                                <option value="500+">أكثر من 500 جنيه</option>
                            </select>

                            <select id="sortBy" onchange="sortCourses()">
                                <option value="default">الترتيب الافتراضي</option>
                                <option value="name">الاسم (أ-ي)</option>
                                <option value="price-low">السعر (الأقل أولاً)</option>
                                <option value="price-high">السعر (الأعلى أولاً)</option>
                                <option value="newest">الأحدث</option>
                            </select>
                        </div>

                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all" onclick="setFilter('all')">
                                <i class="fas fa-th"></i>
                                الكل (<span id="count-all"><?php echo $totalCourses; ?></span>)
                            </button>
                            <button class="filter-btn" data-filter="active" onclick="setFilter('active')">
                                <i class="fas fa-check-circle"></i>
                                مفعل (<span id="count-active"><?php echo $activeCourses; ?></span>)
                            </button>
                            <button class="filter-btn" data-filter="completed" onclick="setFilter('completed')">
                                <i class="fas fa-trophy"></i>
                                مكتمل (<span id="count-completed"><?php echo $completedCoursesCount; ?></span>)
                            </button>
                            <button class="filter-btn" data-filter="pending" onclick="setFilter('pending')">
                                <i class="fas fa-clock"></i>
                                في الانتظار (<span id="count-pending"><?php echo $pendingCourses; ?></span>)
                            </button>
                            <button class="filter-btn" data-filter="inactive" onclick="setFilter('inactive')">
                                <i class="fas fa-shopping-cart"></i>
                                متاح للشراء (<span id="count-inactive"><?php echo $availableCourses; ?></span>)
                            </button>
                        </div>
                    </div>

                    <!-- Results Summary -->
                    <div class="results-summary">
                        <span id="results-count">عرض <?php echo $totalCourses; ?> من <?php echo $totalCourses; ?> كورس</span>
                        <button class="reset-filters" onclick="resetAllFilters()" style="display: none;">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين الفلاتر
                        </button>
                    </div>
                </div>

                <?php if (!empty($courses)): ?>
                    <div class="courses-grid">
                        <?php foreach ($courses as $course): ?>
                            <?php
                            $userCourseStatus = $userCourses[$course['id']] ?? null;
                            $hasAccess = $userCourseStatus && $userCourseStatus['activation_status'] === 'active';
                            $isPending = $userCourseStatus && $userCourseStatus['activation_status'] === 'pending';
                            $isCompleted = isset($completedCourses[$course['id']]);

                            // Set data attributes for filtering
                            $dataAttributes = '';
                            if ($hasAccess) $dataAttributes .= ' data-status="active"';
                            if ($isPending) $dataAttributes .= ' data-status="pending"';
                            if ($isCompleted) $dataAttributes .= ' data-status="completed"';
                            if (!$hasAccess && !$isPending) $dataAttributes .= ' data-status="inactive"';
                            ?>
                            <div class="modern-course-card hover-lift" data-course-id="<?php echo $course['id']; ?>" data-title="<?php echo htmlspecialchars(strtolower($course['title'])); ?>" data-subject="<?php echo htmlspecialchars(strtolower($course['subject'])); ?>"<?php echo $dataAttributes; ?>>
                                <div class="course-image-modern">
                                    <?php if ($course['main_image']): ?>
                                        <img src="<?php echo SITE_URL; ?>/uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" alt="<?php echo htmlspecialchars($course['title']); ?>">
                                    <?php else: ?>
                                        <div class="course-placeholder-modern">
                                            <i class="fas fa-book-open course-icon-modern"></i>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($course['discount_percentage'] > 0): ?>
                                        <div class="discount-badge-modern">
                                            <i class="fas fa-tag"></i>
                                            خصم <?php echo $course['discount_percentage']; ?>%
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($isCompleted): ?>
                                        <div class="completed-badge">
                                            <i class="fas fa-trophy"></i>
                                            مكتمل
                                        </div>
                                    <?php elseif ($hasAccess): ?>
                                        <div class="access-badge">
                                            <i class="fas fa-check-circle"></i>
                                            مفعل
                                        </div>
                                    <?php elseif ($isPending): ?>
                                        <div class="pending-badge">
                                            <i class="fas fa-clock"></i>
                                            معلق
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="course-content-modern">
                                    <div class="course-header-modern">
                                        <h3 class="course-title-modern"><?php echo htmlspecialchars($course['title']); ?></h3>
                                        <span class="course-subject-modern">
                                            <i class="fas fa-tag"></i>
                                            <?php echo htmlspecialchars($course['subject']); ?>
                                        </span>
                                    </div>

                                    <p class="course-description-modern">
                                        <?php echo htmlspecialchars(substr($course['description'], 0, 120)) . (strlen($course['description']) > 120 ? '...' : ''); ?>
                                    </p>

                                    <div class="course-features-preview">
                                        <div class="feature-item-small">
                                            <i class="fas fa-video"></i>
                                            <span>فيديوهات تفاعلية</span>
                                        </div>
                                        <div class="feature-item-small">
                                            <i class="fas fa-tasks"></i>
                                            <span>تمارين وامتحانات</span>
                                        </div>
                                        <div class="feature-item-small">
                                            <i class="fas fa-certificate"></i>
                                            <span>شهادة إتمام</span>
                                        </div>
                                    </div>

                                    <div class="course-price-modern">
                                        <?php if ($course['discount_percentage'] > 0): ?>
                                            <div class="price-container">
                                                <span class="original-price-modern"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                                <span class="discounted-price-modern"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                            </div>
                                        <?php else: ?>
                                            <div class="price-container">
                                                <span class="current-price-modern"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="course-actions-modern">
                                        <?php if ($hasAccess): ?>
                                            <a href="<?php echo SITE_URL; ?>/page/course_content.php?id=<?php echo $course['id']; ?>" class="btn-modern btn-success-modern">
                                                <i class="fas fa-play"></i>
                                                <span>دخول الكورس</span>
                                            </a>
                                        <?php elseif ($isPending): ?>
                                            <button class="btn-modern btn-warning-modern" disabled>
                                                <i class="fas fa-hourglass-half"></i>
                                                <span>في انتظار التفعيل</span>
                                            </button>
                                        <?php else: ?>
                                            <div class="action-buttons-grid">
                                                <a href="<?php echo SITE_URL; ?>/page/course_details.php?id=<?php echo $course['id']; ?>" class="btn-modern btn-outline-modern">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span>التفاصيل</span>
                                                </a>
                                                <a href="<?php echo SITE_URL; ?>/page/course_register.php?id=<?php echo $course['id']; ?>" class="btn-modern btn-primary-modern">
                                                    <i class="fas fa-user-plus"></i>
                                                    <span>سجل الآن</span>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="modern-empty-state">
                        <div class="empty-animation">
                            <div class="empty-icon-modern">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="floating-elements-empty">
                                <div class="float-element-empty">📚</div>
                                <div class="float-element-empty">✨</div>
                                <div class="float-element-empty">🎯</div>
                            </div>
                        </div>
                        <h2 class="empty-title">لا توجد كورسات متاحة حالياً</h2>
                        <p class="empty-description">سيتم إضافة الكورسات المناسبة لمستواك التعليمي قريباً. تابع معنا للحصول على أحدث الكورسات والدورات التدريبية.</p>
                        <div class="empty-actions">
                            <a href="<?php echo SITE_URL; ?>/page/dashboard.php" class="btn-modern btn-primary-modern">
                                <i class="fas fa-home"></i>
                                <span>العودة للرئيسية</span>
                            </a>
                            <a href="#" class="btn-modern btn-outline-modern" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i>
                                <span>تحديث الصفحة</span>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>



    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Enhanced Modern Page Header */
        .modern-page-header {
            margin-bottom: 40px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 50%, #5F9EA0 100%);
            border-radius: 24px;
            padding: 48px;
            box-shadow:
                0 20px 40px rgba(70, 130, 180, 0.2),
                0 10px 20px rgba(135, 206, 235, 0.1);
            color: white;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .modern-page-header::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: headerGlow 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes headerGlow {
            0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
            50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .header-text {
            flex: 1;
        }

        .gradient-title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .gradient-title i {
            font-size: 48px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .header-subtitle {
            font-size: 20px;
            opacity: 0.95;
            line-height: 1.7;
            max-width: 650px;
            font-weight: 400;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .header-stats {
            display: flex;
            gap: 20px;
            padding: 20px 40px;
            margin: 0 auto;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 18px;
            padding: 24px;
            text-align: center;
            min-width: 120px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-icon {
            font-size: 24px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .stat-icon.active {
            color: #FFD700;
        }

        .stat-icon.completed {
            color: #90EE90;
        }

        .stat-icon.progress {
            color: #87CEEB;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.95;
            font-weight: 500;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Enhanced Modern Course Cards */
        .modern-course-card {
            background: white;
            border-radius: 20px;
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.08),
                0 4px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(135, 206, 235, 0.1);
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .modern-course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(135, 206, 235, 0.05) 0%, rgba(70, 130, 180, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .modern-course-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(70, 130, 180, 0.25),
                0 15px 30px rgba(135, 206, 235, 0.15);
            border-color: rgba(135, 206, 235, 0.3);
        }

        .modern-course-card:hover::before {
            opacity: 1;
        }

        /* Modern Course Image */
        .course-image-modern {
            position: relative;
            height: 220px;
            overflow: hidden;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .course-image-modern img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .modern-course-card:hover .course-image-modern img {
            transform: scale(1.05);
        }

        .course-placeholder-modern {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .course-icon-modern {
            font-size: 60px;
            color: white;
            opacity: 0.9;
        }

        .discount-badge-modern {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            animation: pulse 2s infinite;
        }

        .access-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .pending-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .course-content {
            padding: 25px;
        }

        .course-header {
            margin-bottom: 15px;
        }

        .course-title {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-subject {
            color: #4682B4;
            font-size: 14px;
            font-weight: 600;
            background: rgba(135, 206, 235, 0.1);
            padding: 4px 12px;
            border-radius: 15px;
            display: inline-block;
        }

        .course-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .course-price {
            margin-bottom: 20px;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 14px;
            margin-left: 10px;
        }

        .discounted-price {
            color: #dc3545;
            font-size: 20px;
            font-weight: 700;
        }

        .current-price {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
        }

        /* Modern Course Content Styles */
        .course-content-modern {
            padding: 30px;
            background: white;
        }

        .course-header-modern {
            margin-bottom: 20px;
        }

        .course-title-modern {
            font-size: 22px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-subject-modern {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 8px rgba(70, 130, 180, 0.2);
        }

        .course-description-modern {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 14px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-features-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .feature-item-small {
            display: flex;
            align-items: center;
            gap: 5px;
            background: #f8f9fa;
            padding: 6px 10px;
            border-radius: 15px;
            font-size: 11px;
            color: #6c757d;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .feature-item-small:hover {
            background: rgba(135, 206, 235, 0.1);
            border-color: #87CEEB;
        }

        .feature-item-small i {
            color: #4682B4;
            font-size: 12px;
        }

        .course-price-modern {
            margin-bottom: 25px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            text-align: center;
        }

        .price-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .original-price-modern {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 16px;
            opacity: 0.7;
        }

        .discounted-price-modern {
            color: #dc3545;
            font-size: 24px;
            font-weight: 700;
        }

        .current-price-modern {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
        }

        .course-actions-modern {
            margin-top: 25px;
        }

        .action-buttons-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .btn-modern {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
        }

        .btn-success-modern {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            grid-column: 1 / -1;
        }

        .btn-success-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .btn-warning-modern {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
            grid-column: 1 / -1;
            opacity: 0.7;
            cursor: not-allowed;
        }

        .btn-outline-modern {
            background: transparent;
            color: #4682B4;
            border: 2px solid #87CEEB;
            box-shadow: 0 2px 8px rgba(70, 130, 180, 0.1);
        }

        .btn-outline-modern:hover {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.3);
        }

        .course-actions {
            margin-top: 20px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-full {
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-icon {
            font-size: 18px;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-state h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        /* Modern Empty State */
        .modern-empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .empty-animation {
            position: relative;
            margin-bottom: 40px;
        }

        .empty-icon-modern {
            font-size: 100px;
            color: #87CEEB;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        .floating-elements-empty {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 200px;
            pointer-events: none;
        }

        .float-element-empty {
            position: absolute;
            font-size: 24px;
            animation: floatAround 8s ease-in-out infinite;
        }

        .float-element-empty:nth-child(1) {
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }

        .float-element-empty:nth-child(2) {
            top: 60%;
            left: 15%;
            animation-delay: 2s;
        }

        .float-element-empty:nth-child(3) {
            top: 30%;
            left: 80%;
            animation-delay: 4s;
        }

        .empty-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 32px;
            font-weight: 700;
        }

        .empty-description {
            color: #6c757d;
            font-size: 18px;
            margin-bottom: 40px;
            line-height: 1.6;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .empty-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes floatAround {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            33% { transform: translateY(-30px) rotate(120deg); opacity: 1; }
            66% { transform: translateY(15px) rotate(240deg); opacity: 0.8; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Enhanced Mobile Responsive Design */
        @media (max-width: 768px) {
            .page-container {
                padding: 15px;
            }

            .modern-page-header {
                padding: 30px 20px;
            }

            .header-content {
                flex-direction: column;
                gap: 30px;
                text-align: center;
            }

            .gradient-title {
                font-size: 28px;
            }

            .header-stats {
                justify-content: center;
            }

            .courses-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modern-course-card {
                margin-bottom: 20px;
            }

            .course-image-modern {
                height: 200px;
            }

            .course-content-modern {
                padding: 25px;
            }

            .course-title-modern {
                font-size: 20px;
            }

            .course-features-preview {
                justify-content: center;
            }

            .action-buttons-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .btn-modern {
                padding: 12px 20px;
                font-size: 14px;
            }

            .modern-empty-state {
                padding: 60px 20px;
            }

            .empty-icon-modern {
                font-size: 80px;
            }

            .empty-title {
                font-size: 28px;
            }

            .empty-description {
                font-size: 16px;
            }

            .empty-actions {
                flex-direction: column;
                align-items: center;
            }

            .floating-elements-empty {
                width: 150px;
                height: 150px;
            }

            .float-element-empty {
                font-size: 18px;
            }
        }

        /* Extra small devices */
        @media (max-width: 480px) {
            .modern-page-header {
                padding: 20px 15px;
            }

            .gradient-title {
                font-size: 24px;
                flex-direction: column;
                gap: 10px;
            }

            .header-stats {
                flex-direction: column;
                gap: 15px;
            }

            .stat-card {
                min-width: auto;
                padding: 15px;
            }

            .courses-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .course-content-modern {
                padding: 20px;
            }

            .course-title-modern {
                font-size: 18px;
            }

            .course-features-preview {
                gap: 6px;
            }

            .feature-item-small {
                font-size: 10px;
                padding: 4px 8px;
            }

            .btn-modern {
                padding: 10px 16px;
                font-size: 13px;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // البحث والفلترة
        let currentFilter = 'all';

        function filterCourses() {
            const searchTerm = document.getElementById('courseSearch').value.toLowerCase();
            const courseCards = document.querySelectorAll('.modern-course-card');

            courseCards.forEach(card => {
                const title = card.getAttribute('data-title') || '';
                const subject = card.getAttribute('data-subject') || '';
                const status = card.getAttribute('data-status') || '';

                // فحص البحث
                const matchesSearch = title.includes(searchTerm) || subject.includes(searchTerm);

                // فحص الفلتر
                const matchesFilter = currentFilter === 'all' ||
                                    (currentFilter === 'active' && status === 'active') ||
                                    (currentFilter === 'completed' && status === 'completed') ||
                                    (currentFilter === 'pending' && status === 'pending');

                // إظهار أو إخفاء الكارد
                if (matchesSearch && matchesFilter) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                } else {
                    card.style.display = 'none';
                }
            });

            updateResultsCount();
        }

        function setFilter(filter) {
            currentFilter = filter;

            // تحديث أزرار الفلتر
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // تطبيق الفلتر
            filterCourses();
        }

        function updateResultsCount() {
            const visibleCards = document.querySelectorAll('.modern-course-card[style*="display: block"], .modern-course-card:not([style*="display: none"])').length;
            const totalCards = document.querySelectorAll('.modern-course-card').length;

            // يمكن إضافة عداد النتائج هنا إذا أردت
            console.log(`عرض ${visibleCards} من ${totalCards} كورس`);
        }

        // وظائف جديدة للبحث المتقدم
        function clearSearch() {
            document.getElementById('courseSearch').value = '';
            document.querySelector('.clear-search').style.display = 'none';
            filterCourses();
        }

        function resetAllFilters() {
            document.getElementById('courseSearch').value = '';
            document.getElementById('subjectFilter').value = '';
            document.getElementById('priceFilter').value = '';
            document.getElementById('sortBy').value = 'default';
            document.querySelector('.clear-search').style.display = 'none';
            document.querySelector('.reset-filters').style.display = 'none';

            // إعادة تعيين الفلتر النشط
            setFilter('all');
        }

        function sortCourses() {
            const sortBy = document.getElementById('sortBy').value;
            const coursesGrid = document.querySelector('.courses-grid');
            const courses = Array.from(coursesGrid.children);

            courses.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return a.dataset.title.localeCompare(b.dataset.title, 'ar');
                    case 'price-low':
                        const priceA = parseFloat(a.querySelector('.current-price-modern, .discounted-price-modern')?.textContent.replace(/[^\d]/g, '') || 0);
                        const priceB = parseFloat(b.querySelector('.current-price-modern, .discounted-price-modern')?.textContent.replace(/[^\d]/g, '') || 0);
                        return priceA - priceB;
                    case 'price-high':
                        const priceA2 = parseFloat(a.querySelector('.current-price-modern, .discounted-price-modern')?.textContent.replace(/[^\d]/g, '') || 0);
                        const priceB2 = parseFloat(b.querySelector('.current-price-modern, .discounted-price-modern')?.textContent.replace(/[^\d]/g, '') || 0);
                        return priceB2 - priceA2;
                    default:
                        return 0;
                }
            });

            courses.forEach(course => coursesGrid.appendChild(course));
        }

        // تحسين وظيفة الفلترة
        function filterCourses() {
            const searchTerm = document.getElementById('courseSearch').value.toLowerCase();
            const subjectFilter = document.getElementById('subjectFilter').value.toLowerCase();
            const priceFilter = document.getElementById('priceFilter').value;
            const cards = document.querySelectorAll('.modern-course-card');

            let visibleCount = 0;
            let hasActiveFilters = searchTerm || subjectFilter || priceFilter || currentFilter !== 'all';

            // إظهار/إخفاء أزرار التحكم
            document.querySelector('.clear-search').style.display = searchTerm ? 'block' : 'none';
            document.querySelector('.reset-filters').style.display = hasActiveFilters ? 'block' : 'none';

            cards.forEach(card => {
                const title = card.dataset.title || '';
                const subject = card.dataset.subject || '';
                const status = card.dataset.status || '';

                // فحص البحث النصي
                const matchesSearch = !searchTerm || title.includes(searchTerm) || subject.includes(searchTerm);

                // فحص فلتر الموضوع
                const matchesSubject = !subjectFilter || subject.includes(subjectFilter);

                // فحص فلتر السعر
                let matchesPrice = true;
                if (priceFilter) {
                    const priceElement = card.querySelector('.current-price-modern, .discounted-price-modern');
                    const price = priceElement ? parseFloat(priceElement.textContent.replace(/[^\d]/g, '')) : 0;

                    switch (priceFilter) {
                        case 'free':
                            matchesPrice = price === 0;
                            break;
                        case '0-100':
                            matchesPrice = price > 0 && price < 100;
                            break;
                        case '100-500':
                            matchesPrice = price >= 100 && price <= 500;
                            break;
                        case '500+':
                            matchesPrice = price > 500;
                            break;
                    }
                }

                // فحص فلتر الحالة
                const matchesFilter = currentFilter === 'all' ||
                                    (currentFilter === 'active' && status === 'active') ||
                                    (currentFilter === 'completed' && status === 'completed') ||
                                    (currentFilter === 'pending' && status === 'pending') ||
                                    (currentFilter === 'inactive' && status === 'inactive');

                // إظهار أو إخفاء الكارد
                if (matchesSearch && matchesSubject && matchesPrice && matchesFilter) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // تحديث عداد النتائج
            const totalCards = cards.length;
            document.getElementById('results-count').textContent = `عرض ${visibleCount} من ${totalCards} كورس`;
        }

        // تحسين وظيفة تحديث عداد النتائج
        function updateResultsCount() {
            const visibleCards = document.querySelectorAll('.modern-course-card[style*="display: block"], .modern-course-card:not([style*="display: none"])').length;
            const totalCards = document.querySelectorAll('.modern-course-card').length;
            document.getElementById('results-count').textContent = `عرض ${visibleCards} من ${totalCards} كورس`;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateResultsCount();

            // إضافة مستمع للبحث لإظهار زر المسح
            document.getElementById('courseSearch').addEventListener('input', function() {
                const clearBtn = document.querySelector('.clear-search');
                clearBtn.style.display = this.value ? 'block' : 'none';
            });
        });
    </script>

    <style>
        /* Enhanced Search and Filter Styles */
        .search-filter-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(15px);
            border-radius: 24px;
            padding: 36px;
            margin: 32px 0;
            box-shadow:
                0 20px 40px rgba(70, 130, 180, 0.12),
                0 8px 16px rgba(135, 206, 235, 0.08);
            border: 1px solid rgba(135, 206, 235, 0.25);
            position: relative;
            overflow: hidden;
        }

        .search-filter-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #87CEEB 0%, #4682B4 50%, #5F9EA0 100%);
        }

        .search-filter-section::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(135, 206, 235, 0.03) 0%, transparent 70%);
            pointer-events: none;
        }

        .search-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .search-box {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-box input {
            width: 100%;
            padding: 18px 55px 18px 24px;
            border: 2px solid rgba(135, 206, 235, 0.2);
            border-radius: 30px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 4px 12px rgba(135, 206, 235, 0.1),
                inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .search-box input:focus {
            outline: none;
            border-color: #87CEEB;
            background: white;
            box-shadow:
                0 8px 25px rgba(135, 206, 235, 0.2),
                0 0 0 4px rgba(135, 206, 235, 0.1);
            transform: translateY(-2px);
        }

        .search-box input::placeholder {
            color: rgba(108, 117, 125, 0.7);
            transition: color 0.3s ease;
        }

        .search-box input:focus::placeholder {
            color: rgba(135, 206, 235, 0.6);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #87CEEB;
            font-size: 18px;
        }

        .clear-search {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .clear-search:hover {
            background: #f0f0f0;
            color: #666;
        }

        /* Enhanced Statistics */
        .header-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .stat-icon.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stat-icon.completed {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .stat-icon.progress {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* Enhanced Additional Statistics */
        .additional-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 32px;
            position: relative;
            z-index: 2;
        }

        .stat-card-small {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card-small::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card-small:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-card-small:hover::before {
            opacity: 1;
        }

        .stat-card-small i {
            font-size: 20px;
            opacity: 0.9;
            min-width: 20px;
        }

        .stat-text {
            font-size: 15px;
            font-weight: 500;
            opacity: 0.95;
        }

        .stat-card-small i {
            color: #4682B4;
            font-size: 1.2rem;
        }

        .stat-text {
            color: #333;
            font-weight: 500;
        }

        /* Advanced Filters */
        .advanced-filters {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .advanced-filters select {
            padding: 10px 15px;
            border: 2px solid rgba(135, 206, 235, 0.3);
            border-radius: 20px;
            background: white;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .advanced-filters select:focus {
            outline: none;
            border-color: #87CEEB;
            box-shadow: 0 0 10px rgba(135, 206, 235, 0.3);
        }

        /* Results Summary */
        .results-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid rgba(135, 206, 235, 0.2);
        }

        #results-count {
            color: #666;
            font-weight: 500;
        }

        .reset-filters {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .reset-filters:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 12px 24px;
            border: 2px solid rgba(135, 206, 235, 0.3);
            background: white;
            color: #4682B4;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .filter-btn:hover {
            border-color: #87CEEB;
            background: rgba(135, 206, 235, 0.1);
            transform: translateY(-2px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-color: #4682B4;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        /* علامة الكورس المكتمل */
        .completed-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            z-index: 10;
        }

        .completed-badge i {
            font-size: 14px;
        }

        /* تأثيرات الحركة */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .search-container {
                gap: 15px;
            }

            .filter-buttons {
                gap: 10px;
            }

            .filter-btn {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
    <script src="<?php echo SITE_URL; ?>/js/enhanced-ui.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/loading-system.js"></script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
