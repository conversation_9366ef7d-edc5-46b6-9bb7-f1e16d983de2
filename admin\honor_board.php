<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/HonorBoardManager.php';
require_once __DIR__ . '/../includes/SecurityHelper.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$honorBoardManager = new HonorBoardManager();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = SecurityHelper::sanitizeInput([
                    'student_name' => $_POST['student_name'] ?? '',
                    'grade_score' => $_POST['grade_score'] ?? 0,
                    'subject' => $_POST['subject'] ?? '',
                    'ranking_position' => $_POST['ranking_position'] ?? 1,
                    'achievement_type' => $_POST['achievement_type'] ?? 'monthly',
                    'achievement_date' => $_POST['achievement_date'] ?? date('Y-m-d'),
                    'education_level' => $_POST['education_level'] ?? '',
                    'education_type' => $_POST['education_type'] ?? '',
                    'grade' => $_POST['grade'] ?? '',
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : null,
                    'additional_notes' => $_POST['additional_notes'] ?? '',
                    'created_by' => $_SESSION['admin_id']
                ]);

                // Validate data
                $validationErrors = SecurityHelper::validateHonorBoardData($data);
                if (!empty($validationErrors)) {
                    $message = implode('<br>', $validationErrors);
                    $messageType = 'error';
                    break;
                }

                // Check if ranking position is available
                if (!$honorBoardManager->isRankingPositionAvailable(
                    $data['ranking_position'],
                    $data['subject'],
                    $data['achievement_type'],
                    $data['achievement_date'],
                    $data['education_level'],
                    $data['education_type'],
                    $data['grade']
                )) {
                    $message = 'هذا المركز محجوز بالفعل لنفس المادة والتاريخ والمرحلة التعليمية';
                    $messageType = 'error';
                    break;
                }

                if ($honorBoardManager->addHonorEntry($data)) {
                    $message = 'تم إضافة الطالب إلى لوحة الشرف بنجاح';
                    $messageType = 'success';
                    // SecurityHelper::logAdminActivity('add_honor_entry', ['student_name' => $data['student_name']]);
                } else {
                    $message = 'حدث خطأ أثناء إضافة الطالب';
                    $messageType = 'error';
                }
                break;
                
            case 'edit':
                // Check if ranking position is available (excluding current entry)
                if (!$honorBoardManager->isRankingPositionAvailable(
                    $_POST['ranking_position'],
                    $_POST['subject'],
                    $_POST['achievement_type'],
                    $_POST['achievement_date'],
                    $_POST['education_level'],
                    $_POST['education_type'],
                    $_POST['grade'],
                    $_POST['honor_id']
                )) {
                    $message = 'هذا المركز محجوز بالفعل لنفس المادة والتاريخ والمرحلة التعليمية';
                    $messageType = 'error';
                    break;
                }
                
                $data = [
                    'student_name' => $_POST['student_name'],
                    'grade_score' => $_POST['grade_score'],
                    'subject' => $_POST['subject'],
                    'ranking_position' => $_POST['ranking_position'],
                    'achievement_type' => $_POST['achievement_type'],
                    'achievement_date' => $_POST['achievement_date'],
                    'education_level' => $_POST['education_level'],
                    'education_type' => $_POST['education_type'],
                    'grade' => $_POST['grade'],
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : null,
                    'additional_notes' => $_POST['additional_notes']
                ];
                
                if ($honorBoardManager->updateHonorEntry($_POST['honor_id'], $data)) {
                    $message = 'تم تحديث بيانات الطالب بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث بيانات الطالب';
                    $messageType = 'error';
                }
                break;
                
            case 'delete':
                if ($honorBoardManager->deleteHonorEntry($_POST['honor_id'])) {
                    $message = 'تم حذف الطالب من لوحة الشرف بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف الطالب';
                    $messageType = 'error';
                }
                break;
                
            case 'toggle_status':
                if ($honorBoardManager->toggleHonorEntryStatus($_POST['honor_id'])) {
                    $message = 'تم تغيير حالة الطالب بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تغيير حالة الطالب';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get filters
$filters = [];
if (!empty($_GET['education_level'])) $filters['education_level'] = $_GET['education_level'];
if (!empty($_GET['education_type'])) $filters['education_type'] = $_GET['education_type'];
if (!empty($_GET['grade'])) $filters['grade'] = $_GET['grade'];
if (!empty($_GET['subject'])) $filters['subject'] = $_GET['subject'];
if (!empty($_GET['achievement_type'])) $filters['achievement_type'] = $_GET['achievement_type'];
if (!empty($_GET['date_from'])) $filters['date_from'] = $_GET['date_from'];
if (!empty($_GET['date_to'])) $filters['date_to'] = $_GET['date_to'];

$honorEntries = $honorBoardManager->getAllHonorEntries($filters);
$totalEntries = $honorBoardManager->getTotalHonorEntriesCount();
$subjects = $honorBoardManager->getSubjects();

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة لوحة الشرف - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <style>
        .honor-form {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .honor-form h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(70, 130, 180, 0.2);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #87CEEB;
            box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .honor-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .filters-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-toggle {
            background: #ffc107;
            color: #212529;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .ranking-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-weight: bold;
            color: white;
            margin-left: 10px;
        }
        
        .ranking-1 { background: #FFD700; color: #333; }
        .ranking-2 { background: #C0C0C0; color: #333; }
        .ranking-3 { background: #CD7F32; color: white; }
        .ranking-other { background: #6c757d; }
        
        .score-display {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .honor-form {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Add Honor Entry Form -->
                <div class="honor-form">
                    <h3>إضافة طالب إلى لوحة الشرف</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="add">
                        <input type="hidden" name="csrf_token" value="<?php echo SecurityHelper::generateCSRFToken(); ?>">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="student_name">اسم الطالب *</label>
                                <input type="text" id="student_name" name="student_name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="grade_score">الدرجة/النتيجة *</label>
                                <input type="number" id="grade_score" name="grade_score" step="0.01" min="0" max="100" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">المادة *</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="ranking_position">المركز *</label>
                                <input type="number" id="ranking_position" name="ranking_position" min="1" max="100" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="achievement_type">نوع الإنجاز *</label>
                                <select id="achievement_type" name="achievement_type" required>
                                    <option value="">اختر نوع الإنجاز</option>
                                    <option value="monthly">شهري</option>
                                    <option value="semester">فصل دراسي</option>
                                    <option value="yearly">سنوي</option>
                                    <option value="special">خاص</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="achievement_date">تاريخ الإنجاز *</label>
                                <input type="date" id="achievement_date" name="achievement_date" required>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="education_level">المرحلة التعليمية *</label>
                                <select id="education_level" name="education_level" required>
                                    <option value="">اختر المرحلة</option>
                                    <option value="primary">ابتدائي</option>
                                    <option value="preparatory">إعدادي</option>
                                    <option value="secondary">ثانوي</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="education_type">نوع التعليم *</label>
                                <select id="education_type" name="education_type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="general">عام</option>
                                    <option value="azhari">أزهري</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="grade">الصف *</label>
                                <select id="grade" name="grade" required>
                                    <option value="">اختر الصف</option>
                                </select>
                            </div>

                            <div id="specialization_container" class="form-group" style="display: none;">
                                <label for="specialization">التخصص *</label>
                                <select id="specialization" name="specialization">
                                    <option value="">اختر التخصص</option>
                                    <option value="scientific">علمي</option>
                                    <option value="literary">أدبي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="additional_notes">ملاحظات إضافية</label>
                            <textarea id="additional_notes" name="additional_notes" rows="3" placeholder="أي ملاحظات إضافية حول الإنجاز..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn-primary">إضافة إلى لوحة الشرف</button>
                    </form>
                </div>

                <!-- Honor Board List -->
                <div class="honor-table">
                    <div class="table-header">
                        لوحة الشرف (<?php echo count($honorEntries); ?> طالب)
                    </div>

                    <!-- Filters -->
                    <div class="filters-section">
                        <form method="GET">
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label for="filter_education_level">المرحلة التعليمية</label>
                                    <select id="filter_education_level" name="education_level">
                                        <option value="">جميع المراحل</option>
                                        <option value="primary" <?php echo ($_GET['education_level'] ?? '') === 'primary' ? 'selected' : ''; ?>>ابتدائي</option>
                                        <option value="preparatory" <?php echo ($_GET['education_level'] ?? '') === 'preparatory' ? 'selected' : ''; ?>>إعدادي</option>
                                        <option value="secondary" <?php echo ($_GET['education_level'] ?? '') === 'secondary' ? 'selected' : ''; ?>>ثانوي</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_education_type">نوع التعليم</label>
                                    <select id="filter_education_type" name="education_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="general" <?php echo ($_GET['education_type'] ?? '') === 'general' ? 'selected' : ''; ?>>عام</option>
                                        <option value="azhari" <?php echo ($_GET['education_type'] ?? '') === 'azhari' ? 'selected' : ''; ?>>أزهري</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_grade">الصف</label>
                                    <input type="text" id="filter_grade" name="grade" value="<?php echo htmlspecialchars($_GET['grade'] ?? ''); ?>" placeholder="الصف">
                                </div>

                                <div class="form-group">
                                    <label for="filter_subject">المادة</label>
                                    <select id="filter_subject" name="subject">
                                        <option value="">جميع المواد</option>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo htmlspecialchars($subject); ?>" <?php echo ($_GET['subject'] ?? '') === $subject ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($subject); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_achievement_type">نوع الإنجاز</label>
                                    <select id="filter_achievement_type" name="achievement_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="monthly" <?php echo ($_GET['achievement_type'] ?? '') === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                        <option value="semester" <?php echo ($_GET['achievement_type'] ?? '') === 'semester' ? 'selected' : ''; ?>>فصل دراسي</option>
                                        <option value="yearly" <?php echo ($_GET['achievement_type'] ?? '') === 'yearly' ? 'selected' : ''; ?>>سنوي</option>
                                        <option value="special" <?php echo ($_GET['achievement_type'] ?? '') === 'special' ? 'selected' : ''; ?>>خاص</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_date_from">من تاريخ</label>
                                    <input type="date" id="filter_date_from" name="date_from" value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="filter_date_to">إلى تاريخ</label>
                                    <input type="date" id="filter_date_to" name="date_to" value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn-primary">تصفية</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>المركز</th>
                                    <th>اسم الطالب</th>
                                    <th>الدرجة</th>
                                    <th>المادة</th>
                                    <th>نوع الإنجاز</th>
                                    <th>المرحلة/النوع/الصف</th>
                                    <th>تاريخ الإنجاز</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($honorEntries)): ?>
                                    <tr>
                                        <td colspan="9" style="text-align: center; padding: 40px; color: #6c757d;">
                                            لا توجد إدخالات مطابقة للمعايير المحددة
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($honorEntries as $entry): ?>
                                        <tr>
                                            <td>
                                                <span class="ranking-badge ranking-<?php echo $entry['ranking_position'] <= 3 ? $entry['ranking_position'] : 'other'; ?>">
                                                    <?php echo $entry['ranking_position']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($entry['student_name']); ?></strong>
                                                <?php if ($entry['additional_notes']): ?>
                                                    <br><small style="color: #6c757d;"><?php echo htmlspecialchars(substr($entry['additional_notes'], 0, 50)) . (strlen($entry['additional_notes']) > 50 ? '...' : ''); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="score-display"><?php echo number_format($entry['grade_score'], 2); ?>%</span>
                                            </td>
                                            <td><?php echo htmlspecialchars($entry['subject']); ?></td>
                                            <td>
                                                <?php
                                                $achievementTypes = ['monthly' => 'شهري', 'semester' => 'فصل دراسي', 'yearly' => 'سنوي', 'special' => 'خاص'];
                                                echo $achievementTypes[$entry['achievement_type']];
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $educationLevels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي'];
                                                $educationTypes = ['general' => 'عام', 'azhari' => 'أزهري'];
                                                echo $educationLevels[$entry['education_level']] . ' / ' . $educationTypes[$entry['education_type']] . ' / ' . $entry['grade'];
                                                if ($entry['specialization']) {
                                                    $specializations = ['scientific' => 'علمي', 'literary' => 'أدبي'];
                                                    echo ' (' . $specializations[$entry['specialization']] . ')';
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo date('Y/m/d', strtotime($entry['achievement_date'])); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $entry['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                    <?php echo $entry['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-sm btn-edit" onclick="editHonorEntry(<?php echo $entry['id']; ?>)">تعديل</button>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تغيير حالة هذا الطالب؟')">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="honor_id" value="<?php echo $entry['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-toggle">
                                                            <?php echo $entry['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>
                                                        </button>
                                                    </form>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطالب من لوحة الشرف؟')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="honor_id" value="<?php echo $entry['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-delete">حذف</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Honor Entry Modal -->
    <div id="editHonorModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل بيانات الطالب</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editHonorForm" method="POST">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="honor_id" id="edit_honor_id">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_student_name">اسم الطالب *</label>
                            <input type="text" id="edit_student_name" name="student_name" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_grade_score">الدرجة/النتيجة *</label>
                            <input type="number" id="edit_grade_score" name="grade_score" step="0.01" min="0" max="100" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_subject">المادة *</label>
                            <input type="text" id="edit_subject" name="subject" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_ranking_position">المركز *</label>
                            <input type="number" id="edit_ranking_position" name="ranking_position" min="1" max="100" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_achievement_type">نوع الإنجاز *</label>
                            <select id="edit_achievement_type" name="achievement_type" required>
                                <option value="">اختر نوع الإنجاز</option>
                                <option value="monthly">شهري</option>
                                <option value="semester">فصل دراسي</option>
                                <option value="yearly">سنوي</option>
                                <option value="special">خاص</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_achievement_date">تاريخ الإنجاز *</label>
                            <input type="date" id="edit_achievement_date" name="achievement_date" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_education_level">المرحلة التعليمية *</label>
                            <select id="edit_education_level" name="education_level" required>
                                <option value="">اختر المرحلة</option>
                                <option value="primary">ابتدائي</option>
                                <option value="preparatory">إعدادي</option>
                                <option value="secondary">ثانوي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_education_type">نوع التعليم *</label>
                            <select id="edit_education_type" name="education_type" required>
                                <option value="">اختر النوع</option>
                                <option value="general">عام</option>
                                <option value="azhari">أزهري</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_grade">الصف *</label>
                            <select id="edit_grade" name="grade" required>
                                <option value="">اختر الصف</option>
                            </select>
                        </div>

                        <div id="edit_specialization_container" class="form-group" style="display: none;">
                            <label for="edit_specialization">التخصص *</label>
                            <select id="edit_specialization" name="specialization">
                                <option value="">اختر التخصص</option>
                                <option value="scientific">علمي</option>
                                <option value="literary">أدبي</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_additional_notes">ملاحظات إضافية</label>
                        <textarea id="edit_additional_notes" name="additional_notes" rows="3"></textarea>
                    </div>

                    <div class="modal-buttons">
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                        <button type="button" class="btn-secondary" onclick="closeEditModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="<?php echo SITE_URL; ?>/admin/js/education-selector.js"></script>
    <script>
        function editHonorEntry(honorId) {
            // Fetch honor entry data via AJAX
            fetch(`get_honor_entry.php?id=${honorId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const entry = data.entry;
                        document.getElementById('edit_honor_id').value = entry.id;
                        document.getElementById('edit_student_name').value = entry.student_name;
                        document.getElementById('edit_grade_score').value = entry.grade_score;
                        document.getElementById('edit_subject').value = entry.subject;
                        document.getElementById('edit_ranking_position').value = entry.ranking_position;
                        document.getElementById('edit_achievement_type').value = entry.achievement_type;
                        document.getElementById('edit_achievement_date').value = entry.achievement_date;
                        document.getElementById('edit_additional_notes').value = entry.additional_notes || '';

                        // Set education values using the education selector
                        EducationSelector.setValues('edit_', entry.education_level, entry.education_type, entry.grade, entry.specialization);

                        document.getElementById('editHonorModal').style.display = 'block';
                    } else {
                        alert('حدث خطأ في تحميل بيانات الطالب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تحميل بيانات الطالب');
                });
        }

        function closeEditModal() {
            document.getElementById('editHonorModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editHonorModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 95%;
            max-width: 900px;
            max-height: 95vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
