<?php
// نسخة مبسطة من صفحة المنهج لاختبار المشاكل
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    session_start();
    
    // تحميل الإعدادات
    require_once '../config/config.php';
    echo "<!-- تم تحميل الإعدادات بنجاح -->\n";
    
    // تحميل قاعدة البيانات
    require_once '../includes/database.php';
    echo "<!-- تم تحميل قاعدة البيانات بنجاح -->\n";
    
    // فحص تسجيل الدخول
    if (!isset($_SESSION['user_id'])) {
        // إعادة توجيه لصفحة تسجيل الدخول
        header('Location: ' . SITE_URL . '/login.php');
        exit();
    }
    
    // الحصول على بيانات المستخدم
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: ' . SITE_URL . '/login.php');
        exit();
    }
    
    // الحصول على المواد
    $stmt = $db->prepare("SELECT * FROM curriculum_subjects WHERE is_active = 1 LIMIT 10");
    $stmt->execute();
    $subjects = $stmt->fetchAll();
    
} catch (Exception $e) {
    die("خطأ: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنهج - اختبار</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .subject-card { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>المنهج الدراسي - نسخة اختبار</h1>
        
        <div class="success">
            ✅ تم تحميل الصفحة بنجاح!
        </div>
        
        <h2>معلومات المستخدم</h2>
        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['second_name']); ?></p>
        <p><strong>المرحلة التعليمية:</strong> <?php echo htmlspecialchars($user['education_level']); ?></p>
        <p><strong>الصف:</strong> <?php echo htmlspecialchars($user['grade']); ?></p>
        
        <h2>المواد المتاحة</h2>
        <?php if (empty($subjects)): ?>
            <div class="error">
                لا توجد مواد متاحة في قاعدة البيانات
            </div>
        <?php else: ?>
            <?php foreach ($subjects as $subject): ?>
                <div class="subject-card">
                    <h3><?php echo htmlspecialchars($subject['name']); ?></h3>
                    <p><?php echo htmlspecialchars($subject['description'] ?? 'لا يوجد وصف'); ?></p>
                    <p><small>المرحلة: <?php echo htmlspecialchars($subject['education_level']); ?></small></p>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <hr>
        <h2>روابط مفيدة</h2>
        <ul>
            <li><a href="../test.php">صفحة الاختبار الرئيسية</a></li>
            <li><a href="../debug.php">صفحة التشخيص</a></li>
            <li><a href="../index.php">الصفحة الرئيسية</a></li>
            <li><a href="curriculum.php">صفحة المنهج الأصلية</a></li>
        </ul>
        
        <hr>
        <p><small>تم إنشاء هذه الصفحة في: <?php echo date('Y-m-d H:i:s'); ?></small></p>
    </div>
</body>
</html>
