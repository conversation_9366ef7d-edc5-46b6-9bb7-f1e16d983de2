<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    if ($_POST['confirm_delete'] === 'DELETE_ALL_LESSONS') {
        try {
            // Start transaction
            $db->beginTransaction();
            $transaction_started = true;

            // Delete in correct order to avoid foreign key constraints

            // 1. Delete progress tracking data (if tables exist)
            $tables_to_clear = [
                'user_summary_progress',
                'user_exam_progress',
                'user_exercise_progress',
                'user_video_progress',
                'user_lesson_progress',
                'exam_questions',
                'exercise_questions',
                'lesson_summaries',
                'lesson_exams',
                'lesson_exercises',
                'lesson_videos',
                'lessons'
            ];

            foreach ($tables_to_clear as $table) {
                try {
                    $db->exec("DELETE FROM `$table`");
                } catch (Exception $table_error) {
                    // Table might not exist, continue with next table
                    continue;
                }
            }

            // Reset auto increment counters
            $tables_to_reset = [
                'lessons',
                'lesson_videos',
                'lesson_exercises',
                'lesson_exams',
                'lesson_summaries',
                'exercise_questions',
                'exam_questions',
                'user_lesson_progress',
                'user_video_progress',
                'user_exercise_progress',
                'user_exam_progress',
                'user_summary_progress'
            ];

            foreach ($tables_to_reset as $table) {
                try {
                    $db->exec("ALTER TABLE `$table` AUTO_INCREMENT = 1");
                } catch (Exception $reset_error) {
                    // Table might not exist, continue with next table
                    continue;
                }
            }

            // Commit transaction
            $db->commit();
            $transaction_started = false;

            // Delete uploaded summary files
            $summaries_dir = __DIR__ . '/../uploads/summaries/';
            if (is_dir($summaries_dir)) {
                $files = glob($summaries_dir . '*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }

            $success = "تم حذف جميع بيانات الدروس والمحتوى بنجاح!";

        } catch (Exception $e) {
            // Rollback transaction only if it was started
            if (isset($transaction_started) && $transaction_started) {
                try {
                    if ($db->inTransaction()) {
                        $db->rollback();
                    }
                } catch (Exception $rollback_error) {
                    // Ignore rollback errors
                }
            }
            $error = "خطأ في حذف البيانات: " . $e->getMessage();
        }
    } else {
        $error = "يجب كتابة النص التأكيدي بشكل صحيح";
    }
}

// Get statistics
try {
    $stats = [];
    
    // Count lessons
    $stmt = $db->query("SELECT COUNT(*) FROM lessons");
    $stats['lessons'] = $stmt->fetchColumn();
    
    // Count videos
    $stmt = $db->query("SELECT COUNT(*) FROM lesson_videos");
    $stats['videos'] = $stmt->fetchColumn();
    
    // Count exercises
    $stmt = $db->query("SELECT COUNT(*) FROM lesson_exercises");
    $stats['exercises'] = $stmt->fetchColumn();
    
    // Count exercise questions
    $stmt = $db->query("SELECT COUNT(*) FROM exercise_questions");
    $stats['exercise_questions'] = $stmt->fetchColumn();
    
    // Count exams
    $stmt = $db->query("SELECT COUNT(*) FROM lesson_exams");
    $stats['exams'] = $stmt->fetchColumn();
    
    // Count exam questions
    $stmt = $db->query("SELECT COUNT(*) FROM exam_questions");
    $stats['exam_questions'] = $stmt->fetchColumn();
    
    // Count summaries
    $stmt = $db->query("SELECT COUNT(*) FROM lesson_summaries");
    $stats['summaries'] = $stmt->fetchColumn();
    
    // Count progress records
    $stmt = $db->query("SELECT COUNT(*) FROM user_lesson_progress");
    $stats['lesson_progress'] = $stmt->fetchColumn();
    
    $stmt = $db->query("SELECT COUNT(*) FROM user_video_progress");
    $stats['video_progress'] = $stmt->fetchColumn();
    
    $stmt = $db->query("SELECT COUNT(*) FROM user_exercise_progress");
    $stats['exercise_progress'] = $stmt->fetchColumn();
    
    $stmt = $db->query("SELECT COUNT(*) FROM user_exam_progress");
    $stats['exam_progress'] = $stmt->fetchColumn();
    
    $stmt = $db->query("SELECT COUNT(*) FROM user_summary_progress");
    $stats['summary_progress'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $stats_error = "خطأ في جلب الإحصائيات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حذف جميع بيانات الدروس - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <?php include __DIR__ . '/../includes/admin_header.php'; ?>
        
        <div class="admin-container">
            <?php include __DIR__ . '/../includes/admin_sidebar.php'; ?>
            
            <main class="admin-main">
                <div class="admin-header danger-header">
                    <div class="header-content">
                        <div class="page-info">
                            <h1><i class="fas fa-exclamation-triangle"></i> حذف جميع بيانات الدروس</h1>
                            <p class="danger-text">تحذير: هذا الإجراء سيحذف جميع الدروس والمحتوى نهائياً ولا يمكن التراجع عنه!</p>
                        </div>
                        <div class="header-actions">
                            <a href="lessons.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للدروس
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Card -->
                <div class="stats-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> إحصائيات البيانات الحالية</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($stats_error)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <?php echo $stats_error; ?>
                            </div>
                        <?php else: ?>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon lessons">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['lessons']; ?></span>
                                        <span class="stat-label">درس</span>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon videos">
                                        <i class="fas fa-video"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['videos']; ?></span>
                                        <span class="stat-label">فيديو</span>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon exercises">
                                        <i class="fas fa-dumbbell"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['exercises']; ?></span>
                                        <span class="stat-label">تدريب</span>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon exams">
                                        <i class="fas fa-clipboard-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['exams']; ?></span>
                                        <span class="stat-label">امتحان</span>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon summaries">
                                        <i class="fas fa-file-pdf"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['summaries']; ?></span>
                                        <span class="stat-label">ملخص</span>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon questions">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number"><?php echo $stats['exercise_questions'] + $stats['exam_questions']; ?></span>
                                        <span class="stat-label">سؤال</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="progress-stats">
                                <h4>بيانات تتبع التقدم:</h4>
                                <ul>
                                    <li>تقدم الدروس: <?php echo $stats['lesson_progress']; ?> سجل</li>
                                    <li>تقدم الفيديوهات: <?php echo $stats['video_progress']; ?> سجل</li>
                                    <li>تقدم التدريبات: <?php echo $stats['exercise_progress']; ?> سجل</li>
                                    <li>تقدم الامتحانات: <?php echo $stats['exam_progress']; ?> سجل</li>
                                    <li>تقدم الملخصات: <?php echo $stats['summary_progress']; ?> سجل</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Warning Card -->
                <div class="warning-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h3>
                    </div>
                    <div class="card-body">
                        <div class="warning-content">
                            <p><strong>سيتم حذف البيانات التالية نهائياً:</strong></p>
                            <ul class="warning-list">
                                <li><i class="fas fa-times-circle"></i> جميع الدروس</li>
                                <li><i class="fas fa-times-circle"></i> جميع الفيديوهات</li>
                                <li><i class="fas fa-times-circle"></i> جميع التدريبات وأسئلتها</li>
                                <li><i class="fas fa-times-circle"></i> جميع الامتحانات وأسئلتها</li>
                                <li><i class="fas fa-times-circle"></i> جميع الملخصات وملفاتها</li>
                                <li><i class="fas fa-times-circle"></i> جميع بيانات تتبع تقدم الطلاب</li>
                            </ul>
                            
                            <div class="warning-note">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة:</strong> لن يتم حذف الأقسام الدراسية أو بيانات المستخدمين أو الاشتراكات.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Form -->
                <div class="delete-card">
                    <div class="card-header">
                        <h3><i class="fas fa-trash-alt"></i> تأكيد الحذف</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="delete-form" onsubmit="return confirmDelete()">
                            <div class="form-group">
                                <label for="confirm_delete">
                                    لتأكيد الحذف، اكتب النص التالي بالضبط: 
                                    <code>DELETE_ALL_LESSONS</code>
                                </label>
                                <input type="text" name="confirm_delete" id="confirm_delete" class="form-control" 
                                       placeholder="اكتب: DELETE_ALL_LESSONS" required>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-danger btn-large" id="deleteBtn" disabled>
                                    <i class="fas fa-trash-alt"></i>
                                    حذف جميع البيانات نهائياً
                                </button>
                                <a href="lessons.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .admin-container {
            display: flex;
            flex: 1;
        }

        .admin-main {
            flex: 1;
            padding: 20px;
            margin-left: 280px;
        }

        .danger-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            margin-bottom: 30px;
        }

        .danger-text {
            color: #fff3cd !important;
            font-weight: 600;
            margin: 0;
        }

        .stats-card, .warning-card, .delete-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-card .card-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .warning-card .card-header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .delete-card .card-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .card-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-icon.lessons { background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%); }
        .stat-icon.videos { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }
        .stat-icon.exercises { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .stat-icon.exams { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .stat-icon.summaries { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
        .stat-icon.questions { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .progress-stats {
            background: rgba(23, 162, 184, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #17a2b8;
        }

        .progress-stats h4 {
            color: #17a2b8;
            margin: 0 0 15px 0;
            font-size: 16px;
        }

        .progress-stats ul {
            margin: 0;
            padding-right: 20px;
        }

        .progress-stats li {
            color: #6c757d;
            margin-bottom: 8px;
        }

        .warning-content {
            color: #856404;
        }

        .warning-list {
            margin: 20px 0;
            padding-right: 20px;
        }

        .warning-list li {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .warning-list i {
            color: #dc3545;
        }

        .warning-note {
            background: rgba(255, 193, 7, 0.2);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin-top: 20px;
        }

        .delete-form {
            max-width: 500px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            color: #dc3545;
            font-weight: bold;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-danger:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
                padding: 10px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-item {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .form-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>

    <script>
        // Enable delete button only when correct text is entered
        document.getElementById('confirm_delete').addEventListener('input', function() {
            const deleteBtn = document.getElementById('deleteBtn');
            if (this.value === 'DELETE_ALL_LESSONS') {
                deleteBtn.disabled = false;
                deleteBtn.style.opacity = '1';
            } else {
                deleteBtn.disabled = true;
                deleteBtn.style.opacity = '0.5';
            }
        });

        function confirmDelete() {
            const confirmText = document.getElementById('confirm_delete').value;
            if (confirmText !== 'DELETE_ALL_LESSONS') {
                alert('يجب كتابة النص التأكيدي بالضبط: DELETE_ALL_LESSONS');
                return false;
            }
            
            return confirm('هل أنت متأكد من حذف جميع بيانات الدروس؟ هذا الإجراء لا يمكن التراجع عنه!');
        }
    </script>
</body>
</html>
