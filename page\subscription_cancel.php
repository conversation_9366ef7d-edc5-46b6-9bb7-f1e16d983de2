<?php
session_start();
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit();
}

$db = Database::getInstance()->getConnection();
$message = '';
$error = '';

// Get subscription ID from URL
$subscription_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$subscription_id) {
    header('Location: ' . SITE_URL . '/page/curriculum.php');
    exit();
}

// Verify subscription belongs to user
$stmt = $db->prepare("
    SELECT us.*, sp.name as plan_name, sp.color, sp.icon
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.id = ? AND us.user_id = ? AND us.payment_status = 'completed'
");
$stmt->execute([$subscription_id, $_SESSION['user_id']]);
$subscription = $stmt->fetch();

if (!$subscription) {
    header('Location: ' . SITE_URL . '/page/curriculum.php');
    exit();
}

// Handle cancellation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_cancel'])) {
    $cancellation_reason = trim($_POST['cancellation_reason'] ?? '');
    
    try {
        $db->beginTransaction();
        
        // Update subscription status
        $stmt = $db->prepare("
            UPDATE user_subscriptions 
            SET cancelled_at = NOW(), 
                cancellation_reason = ?,
                payment_status = 'cancelled'
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$cancellation_reason, $subscription_id, $_SESSION['user_id']]);
        
        // Update user subscription status
        $stmt = $db->prepare("
            UPDATE users 
            SET subscription_status = 'cancelled',
                current_plan_id = NULL
            WHERE id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        
        $db->commit();
        
        $message = 'تم إلغاء الاشتراك بنجاح. سيتم إيقاف الخدمة في تاريخ انتهاء الاشتراك الحالي.';
        
        // Redirect after 3 seconds
        header("refresh:3;url=" . SITE_URL . "/page/curriculum.php");
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'حدث خطأ أثناء إلغاء الاشتراك. يرجى المحاولة مرة أخرى.';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إلغاء الاشتراك - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .cancel-subscription-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .cancel-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
            border: 1px solid #e2e8f0;
            width: 100%;
        }

        .cancel-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .cancel-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }

        .cancel-title {
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .cancel-subtitle {
            color: #6c757d;
            line-height: 1.6;
        }

        .subscription-info {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
        }

        .subscription-details {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .plan-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .plan-info h3 {
            margin: 0 0 5px 0;
            font-size: 1.3rem;
        }

        .plan-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: inherit;
            resize: vertical;
            min-height: 100px;
            box-sizing: border-box;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #4682B4;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .warning-box .warning-icon {
            color: #856404;
            margin-left: 8px;
        }

        .warning-box p {
            margin: 0;
            color: #856404;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .cancel-subscription-container {
                padding: 15px;
            }

            .cancel-card {
                padding: 20px;
            }

            .subscription-details {
                flex-direction: column;
                text-align: center;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="cancel-subscription-container">
        <div class="cancel-card">
            <?php if ($message): ?>
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $message; ?>
                </div>
            <?php elseif ($error): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <div class="cancel-header">
                <div class="cancel-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1 class="cancel-title">إلغاء الاشتراك</h1>
                <p class="cancel-subtitle">هل أنت متأكد من رغبتك في إلغاء اشتراكك؟ سيتم إيقاف جميع الخدمات في تاريخ انتهاء الاشتراك الحالي.</p>
            </div>

            <div class="subscription-info">
                <div class="subscription-details">
                    <div class="plan-icon">
                        <?php echo $subscription['icon']; ?>
                    </div>
                    <div class="plan-info">
                        <h3><?php echo htmlspecialchars($subscription['plan_name']); ?></h3>
                        <p>ينتهي في: <?php echo date('Y/m/d', strtotime($subscription['end_date'])); ?></p>
                        <p>المبلغ المدفوع: <?php echo number_format($subscription['amount_paid'], 2); ?> جنيه</p>
                    </div>
                </div>
            </div>

            <div class="warning-box">
                <p>
                    <i class="fas fa-info-circle warning-icon"></i>
                    بعد إلغاء الاشتراك، ستفقد الوصول إلى جميع المحتويات المدفوعة في تاريخ انتهاء الاشتراك الحالي.
                </p>
            </div>

            <?php if (!$message): ?>
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="cancellation_reason">سبب الإلغاء (اختياري):</label>
                        <textarea id="cancellation_reason" name="cancellation_reason" placeholder="يرجى إخبارنا بسبب إلغاء الاشتراك لمساعدتنا في تحسين خدماتنا..."></textarea>
                    </div>

                    <div class="action-buttons">
                        <button type="submit" name="confirm_cancel" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من إلغاء الاشتراك؟ هذا الإجراء لا يمكن التراجع عنه.')">
                            <i class="fas fa-times"></i>
                            تأكيد الإلغاء
                        </button>
                        <a href="<?php echo SITE_URL; ?>/page/curriculum.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للمنهج
                        </a>
                    </div>
                </form>
            <?php else: ?>
                <div class="action-buttons">
                    <a href="<?php echo SITE_URL; ?>/page/curriculum.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للمنهج
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
