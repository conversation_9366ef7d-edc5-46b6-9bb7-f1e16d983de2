<?php
/**
 * <PERSON><PERSON><PERSON> to update all admin files with new design
 * This script will update CSS and JS paths in all admin PHP files
 */

// List of admin PHP files to update
$adminFiles = [
    'activation_codes.php',
    'add_admin.php',
    'course_content.php',
    'course_subscribers.php',
    'courses.php',
    'create_test_admin.php',
    'curriculum_subjects.php',
    'exams.php',
    'extend_subscription.php',
    'get_exam.php',
    'get_honor_entry.php',
    'get_news.php',
    'honor_board.php',
    'login.php',
    'manage_notifications.php',
    'manage_questions.php',
    'messages.php',
    'news.php',
    'payment_requests.php',
    'reset_admin.php',
    'send_notifications.php',
    'student_notes.php',
    'subscribers.php',
    'subscription_plans.php',
    'test_notifications_system.php',
    'test_session.php',
    'users.php'
];

// Function to update file content
function updateFileContent($filePath) {
    if (!file_exists($filePath)) {
        echo "File not found: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Update CSS links
    $content = preg_replace(
        '/href="\.\.\/css\/styles\.css"/',
        'href="css/admin-modern.css"',
        $content
    );
    
    $content = preg_replace(
        '/href="\<\?php echo SITE_URL; \?\>\/admin\/css\/admin-styles\.css"/',
        'href="css/admin-modern.css"',
        $content
    );
    
    $content = preg_replace(
        '/href="css\/enhanced-admin-styles\.css"/',
        'href="css/admin-modern.css"',
        $content
    );
    
    // Add admin-fixes.css if not present
    if (strpos($content, 'admin-fixes.css') === false && strpos($content, 'admin-modern.css') !== false) {
        $content = str_replace(
            'href="css/admin-modern.css">',
            'href="css/admin-modern.css">' . "\n" . '    <link rel="stylesheet" href="css/admin-fixes.css">',
            $content
        );
    }
    
    // Update notifications.css path
    $content = preg_replace(
        '/href="\<\?php echo SITE_URL; \?\>\/css\/notifications\.css"/',
        'href="../css/notifications.css"',
        $content
    );
    
    // Update JavaScript paths
    $content = preg_replace(
        '/src="\<\?php echo SITE_URL; \?\>\/js\/notifications\.js"/',
        'src="../js/notifications.js"',
        $content
    );
    
    // Add admin-modern.js if not present
    if (strpos($content, 'admin-modern.js') === false && strpos($content, '</body>') !== false) {
        $content = str_replace(
            '</body>',
            '    <script src="js/admin-modern.js"></script>' . "\n" . '</body>',
            $content
        );
    }
    
    // Update HTML structure for modern design
    if (strpos($content, 'admin-layout') !== false) {
        // Replace old layout with new layout
        $content = str_replace(
            '<div class="admin-layout">',
            '<div class="admin-container">',
            $content
        );
        
        // Add header include if not present
        if (strpos($content, 'includes/header.php') === false) {
            $content = str_replace(
                '<body>',
                '<body>' . "\n" . '    <?php include \'includes/header.php\'; ?>' . "\n",
                $content
            );
        }
        
        // Update main content structure
        $content = str_replace(
            '<main class="admin-main">',
            '<div class="main-content">',
            $content
        );
        
        $content = str_replace(
            '</main>',
            '</div>',
            $content
        );
        
        $content = str_replace(
            '<div class="admin-content">',
            '<!-- Page Header -->' . "\n" . '            <div class="page-header">' . "\n" . '                <h1>',
            $content
        );
    }
    
    // Add Google Fonts if not present
    if (strpos($content, 'fonts.googleapis.com') === false && strpos($content, '</head>') !== false) {
        $googleFonts = '    <link rel="preconnect" href="https://fonts.googleapis.com">' . "\n" .
                      '    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n" .
                      '    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">' . "\n";
        
        $content = str_replace('</head>', $googleFonts . '</head>', $content);
    }
    
    // Update SITE_NAME references
    $content = preg_replace(
        '/\<\?php echo SITE_NAME; \?\>/',
        '<?php echo defined(\'SITE_NAME\') ? SITE_NAME : \'لوحة التحكم\'; ?>',
        $content
    );
    
    // Save updated content if changes were made
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "Updated: $filePath\n";
        return true;
    } else {
        echo "No changes needed: $filePath\n";
        return false;
    }
}

// Main execution
echo "Starting admin files update...\n\n";

$updatedCount = 0;
$totalFiles = count($adminFiles);

foreach ($adminFiles as $file) {
    $filePath = __DIR__ . '/' . $file;
    if (updateFileContent($filePath)) {
        $updatedCount++;
    }
}

echo "\n=== Update Summary ===\n";
echo "Total files processed: $totalFiles\n";
echo "Files updated: $updatedCount\n";
echo "Files unchanged: " . ($totalFiles - $updatedCount) . "\n";

// Create backup of original files
$backupDir = __DIR__ . '/backup_' . date('Y-m-d_H-i-s');
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "\nBackup directory created: $backupDir\n";
}

echo "\nAdmin files update completed!\n";
echo "Please test the updated files and check for any issues.\n";
echo "If you encounter problems, you can restore from the backup directory.\n";
?>
