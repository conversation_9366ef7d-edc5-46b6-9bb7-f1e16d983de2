<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';
require_once '../includes/FawryGateway.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$fawryGateway = new FawryGateway();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get user information
$stmt = $db->prepare("SELECT username, email, full_name, phone FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login.php');
    exit;
}

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'active') {
    header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
    exit;
}

$message = '';
$messageType = '';
$paymentData = null;

// Calculate final price
$finalPrice = $course['discount_percentage'] > 0 ? $course['discounted_price'] : $course['price'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_payment'])) {
        // Prepare customer info
        $customerInfo = [
            'name' => $user['full_name'] ?? $user['username'],
            'email' => $user['email'],
            'mobile' => $user['phone'] ?? '01000000000', // Default if no phone
            'course_title' => $course['title']
        ];
        
        // Create Fawry payment request
        $paymentResult = $fawryGateway->createPaymentRequest($userId, $courseId, $finalPrice, $customerInfo);
        
        if ($paymentResult['success']) {
            $paymentData = $paymentResult;
        } else {
            $message = $paymentResult['error'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدفع عبر فوري - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="<?php echo SITE_URL; ?>/page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="<?php echo SITE_URL; ?>/page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="<?php echo SITE_URL; ?>/page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>الدفع عبر فوري</span>
                </div>

                <div class="payment-container">
                    <!-- Course Info Card -->
                    <div class="course-info-card">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="<?php echo SITE_URL; ?>/uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-details">
                            <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                            <p><?php echo htmlspecialchars($course['subject']); ?></p>
                            <div class="course-price">
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <span class="original-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <span class="discounted-price"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <div class="payment-form-card">
                        <?php if (!$paymentData): ?>
                            <div class="form-header">
                                <div class="payment-icon">🏪</div>
                                <h1>الدفع عبر فوري</h1>
                                <p>ادفع بسهولة من أي فرع فوري أو عبر التطبيق</p>
                            </div>

                            <!-- Fawry Info -->
                            <div class="fawry-info">
                                <h3>🏪 مميزات الدفع عبر فوري</h3>
                                <div class="features-grid">
                                    <div class="feature">
                                        <div class="feature-icon">⚡</div>
                                        <h4>تفعيل فوري</h4>
                                        <p>يتم تفعيل الكورس فور إتمام الدفع</p>
                                    </div>
                                    <div class="feature">
                                        <div class="feature-icon">🔒</div>
                                        <h4>دفع آمن</h4>
                                        <p>نظام دفع آمن ومضمون</p>
                                    </div>
                                    <div class="feature">
                                        <div class="feature-icon">🏪</div>
                                        <h4>متاح في كل مكان</h4>
                                        <p>أكثر من 100,000 نقطة دفع في مصر</p>
                                    </div>
                                    <div class="feature">
                                        <div class="feature-icon">📱</div>
                                        <h4>عبر التطبيق</h4>
                                        <p>ادفع من تطبيق فوري على هاتفك</p>
                                    </div>
                                </div>
                            </div>

                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?>">
                                    <?php echo htmlspecialchars($message); ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" class="payment-form">
                                <div class="payment-summary">
                                    <h3>ملخص الدفع</h3>
                                    <div class="summary-row">
                                        <span>الكورس:</span>
                                        <span><?php echo htmlspecialchars($course['title']); ?></span>
                                    </div>
                                    <div class="summary-row">
                                        <span>المبلغ:</span>
                                        <span class="amount"><?php echo number_format($finalPrice, 0); ?> جنيه</span>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" name="confirm_payment" class="btn btn-primary btn-large">
                                        <span class="btn-icon">🏪</span>
                                        متابعة الدفع عبر فوري
                                    </button>
                                    <a href="<?php echo SITE_URL; ?>/page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                        <span class="btn-icon">↩️</span>
                                        العودة لخيارات التفعيل
                                    </a>
                                </div>
                            </form>
                        <?php else: ?>
                            <!-- Payment Instructions -->
                            <div class="form-header">
                                <div class="payment-icon">✅</div>
                                <h1>تم إنشاء طلب الدفع</h1>
                                <p>اتبع التعليمات أدناه لإتمام الدفع</p>
                            </div>

                            <div class="payment-instructions">
                                <h3>📋 تعليمات الدفع</h3>
                                <div class="instruction-steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>اذهب لأقرب فرع فوري</h4>
                                            <p>أو استخدم تطبيق فوري على هاتفك</p>
                                        </div>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>أعطي الكود التالي</h4>
                                            <div class="payment-code">
                                                <strong><?php echo $paymentData['reference_number']; ?></strong>
                                                <button onclick="copyCode()" class="copy-btn">نسخ</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>ادفع المبلغ</h4>
                                            <p><strong><?php echo number_format($finalPrice, 0); ?> جنيه</strong></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="payment-status" id="paymentStatus">
                                <div class="status-checking">
                                    <div class="spinner"></div>
                                    <p>جاري التحقق من حالة الدفع...</p>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button onclick="checkPaymentStatus()" class="btn btn-primary btn-large">
                                    <span class="btn-icon">🔄</span>
                                    تحديث حالة الدفع
                                </button>
                                <a href="<?php echo SITE_URL; ?>/page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    إلغاء والعودة
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .course-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-details p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .course-price {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 14px;
        }

        .discounted-price {
            color: #dc3545;
            font-size: 18px;
            font-weight: 700;
        }

        .current-price {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 700;
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .payment-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #6c757d;
            font-size: 18px;
        }

        .payment-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .payment-summary h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .summary-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
        }

        .amount {
            color: #28a745;
            font-weight: bold;
        }

        .payment-instructions {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .payment-instructions h3 {
            color: #1976d2;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .instruction-steps {
            display: grid;
            gap: 20px;
        }

        .step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .step-number {
            background: #2196f3;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: #1976d2;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .step-content p {
            color: #1976d2;
            line-height: 1.5;
            margin: 0;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 30px;
        }

        .course-info-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .payment-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .fawry-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4caf50;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
        }

        .payment-code {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 15px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 5px;
            left: 5px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function copyCode() {
            const code = '<?php echo $paymentData['reference_number'] ?? ''; ?>';
            navigator.clipboard.writeText(code).then(function() {
                alert('تم نسخ الكود بنجاح!');
            });
        }

        <?php if ($paymentData): ?>
        // Auto-check payment status every 10 seconds
        let statusCheckInterval = setInterval(checkPaymentStatus, 10000);

        function checkPaymentStatus() {
            fetch('<?php echo SITE_URL; ?>/api/check_fawry_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transaction_id: '<?php echo $paymentData['transaction_id']; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.status === 'PAID') {
                    clearInterval(statusCheckInterval);
                    document.getElementById('paymentStatus').innerHTML = 
                        '<div class="status-success"><h3>✅ تم الدفع بنجاح!</h3><p>جاري تحويلك لصفحة الكورس...</p></div>';
                    setTimeout(() => {
                        window.location.href = 'course_content.php?id=<?php echo $courseId; ?>';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error checking payment status:', error);
            });
        }
        <?php endif; ?>
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
