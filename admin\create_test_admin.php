<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إنشاء إدارة تجريبية</h2>";

try {
    $adminManager = new AdminManager();
    
    // Check if test admin already exists
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT * FROM admins WHERE username = 'testadmin'");
    $stmt->execute();
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "✅ الإدارة التجريبية موجودة بالفعل<br>";
        echo "اسم المستخدم: testadmin<br>";
        echo "كلمة المرور: test123<br>";
        echo "معرف الإدارة: " . $existingAdmin['id'] . "<br>";
    } else {
        // Create test admin
        $adminData = [
            'username' => 'testadmin',
            'email' => '<EMAIL>',
            'password' => 'test123',
            'full_name' => 'Test Administrator',
            'role' => 'admin'
        ];
        
        $adminId = $adminManager->createAdmin($adminData);
        
        if ($adminId) {
            echo "✅ تم إنشاء الإدارة التجريبية بنجاح<br>";
            echo "معرف الإدارة: " . $adminId . "<br>";
            echo "اسم المستخدم: testadmin<br>";
            echo "كلمة المرور: test123<br>";
        } else {
            echo "❌ فشل في إنشاء الإدارة التجريبية<br>";
        }
    }
    
    // Also ensure system admin exists (ID = 1)
    $stmt2 = $db->prepare("SELECT * FROM admins WHERE id = 1");
    $stmt2->execute();
    $systemAdmin = $stmt2->fetch();
    
    if (!$systemAdmin) {
        echo "<br><h3>إنشاء إدارة النظام (ID = 1)</h3>";
        
        $systemAdminData = [
            'username' => 'system',
            'email' => '<EMAIL>',
            'password' => 'system123',
            'full_name' => 'System Administrator',
            'role' => 'super_admin'
        ];
        
        $systemAdminId = $adminManager->createAdmin($systemAdminData);
        
        if ($systemAdminId) {
            // Force ID to be 1
            $stmt3 = $db->prepare("UPDATE admins SET id = 1 WHERE id = ?");
            $stmt3->execute([$systemAdminId]);
            
            // Reset auto increment
            $db->exec("ALTER TABLE admins AUTO_INCREMENT = 2");
            
            echo "✅ تم إنشاء إدارة النظام بنجاح (ID = 1)<br>";
        } else {
            echo "❌ فشل في إنشاء إدارة النظام<br>";
        }
    } else {
        echo "<br>✅ إدارة النظام موجودة (ID = 1)<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
}

echo "<br><a href=\"" . SITE_URL . "/admin/login.php\">تسجيل دخول الإدارة</a>";
echo "<br><a href='test_session.php'>اختبار جلسة الإدارة</a>";
?>
