<?php
session_start();
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit();
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Get user information
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        header('Location: ' . SITE_URL . '/login.php');
        exit();
    }

    // Ensure user has a name field, fallback to username or email
    if (empty($user['name'])) {
        $user['name'] = $user['username'] ?? $user['email'] ?? 'مستخدم';
    }
    
    // Get curriculum subjects based on user's education info
    $sql = "SELECT * FROM curriculum_subjects 
            WHERE is_active = 1 
            AND (education_level = ? OR education_level = 'all')
            AND (education_type = ? OR education_type = 'all')
            AND (grade = ? OR grade = 'all')";
    
    $params = [$user['education_level'], $user['education_type'], $user['grade']];
    
    // Add specialization filter for secondary students
    if ($user['education_level'] === 'secondary' && !empty($user['specialization'])) {
        $sql .= " AND (specialization = ? OR specialization = 'all')";
        $params[] = $user['specialization'];
    } else {
        $sql .= " AND (specialization = 'all')";
    }
    
    $sql .= " ORDER BY sort_order ASC, name ASC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $subjects = $stmt->fetchAll();
    
    // Group subjects by education level for better organization
    $groupedSubjects = [];
    foreach ($subjects as $subject) {
        $level = $subject['education_level'];
        if (!isset($groupedSubjects[$level])) {
            $groupedSubjects[$level] = [];
        }
        $groupedSubjects[$level][] = $subject;
    }

    // Get user's current subscription
    $currentSubscription = null;
    $stmt = $db->prepare("
        SELECT us.*, sp.name as plan_name, sp.description, sp.color, sp.icon, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.payment_status = 'completed' AND us.end_date > NOW()
        ORDER BY us.end_date DESC
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $currentSubscription = $stmt->fetch();

    // Get curriculum statistics
    $stmt = $db->prepare("
        SELECT
            COUNT(DISTINCT cs.id) as total_subjects,
            COUNT(DISTINCT CASE WHEN cs.education_level = ? THEN cs.id END) as level_subjects,
            COUNT(DISTINCT CASE WHEN cs.education_level = 'all' THEN cs.id END) as general_subjects
        FROM curriculum_subjects cs
        WHERE cs.is_active = 1
        AND (cs.education_level = ? OR cs.education_level = 'all')
        AND (cs.education_type = ? OR cs.education_type = 'all')
        AND (cs.grade = ? OR cs.grade = 'all')
    ");
    $stmt->execute([$user['education_level'], $user['education_level'], $user['education_type'], $user['grade']]);
    $curriculumStats = $stmt->fetch();

} catch (Exception $e) {
    $error = "حدث خطأ في تحميل البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنهج - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/enhanced-ui.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/loading-system.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Modern Curriculum Layout - Dashboard Integration */
        .curriculum-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            width: 100%;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Improved spacing and layout */
        .curriculum-header {
            margin-bottom: 32px;
        }

        .user-info-card {
            margin-bottom: 32px;
        }

        .subscription-card,
        .no-subscription-card {
            margin-bottom: 32px;
        }

        .stats-cards {
            margin-bottom: 40px;
        }

        .subjects-section {
            margin-bottom: 48px;
        }

        .subjects-section:last-child {
            margin-bottom: 32px;
        }

        .curriculum-header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 30px 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
            position: relative;
            overflow: hidden;
        }

        .curriculum-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        .curriculum-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .curriculum-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 2;
        }

        .user-info-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
            border-right: 4px solid #4682B4;
            transition: all 0.3s ease;
        }

        .user-info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.15);
        }

        .user-info-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .user-info-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 14px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .info-item:hover {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.2);
        }

        .info-item:hover .icon {
            color: white !important;
        }

        .info-item .icon {
            font-size: 1.1rem;
            color: #4682B4;
            transition: color 0.3s ease;
        }

        /* Subscription Card Styles */
        .subscription-card {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .subscription-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .subscription-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(70, 130, 180, 0.4);
        }

        .subscription-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .subscription-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .subscription-info h3 {
            margin: 0 0 5px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .subscription-status {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            backdrop-filter: blur(10px);
        }

        .subscription-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .subscription-detail {
            text-align: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .subscription-detail .label {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .subscription-detail .value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .subscription-actions {
            display: flex;
            gap: 12px;
            position: relative;
            z-index: 2;
        }

        .btn-cancel {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            border: 2px solid rgba(220, 53, 69, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-cancel:hover {
            background: rgba(220, 53, 69, 1);
            border-color: rgba(220, 53, 69, 0.8);
            transform: translateY(-2px);
        }

        .btn-settings {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-settings:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .no-subscription-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
            border: 2px dashed #87CEEB;
            text-align: center;
            transition: all 0.3s ease;
        }

        .no-subscription-card:hover {
            border-color: #4682B4;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.15);
        }

        .no-subscription-icon {
            font-size: 3rem;
            color: #87CEEB;
            margin-bottom: 15px;
        }

        .no-subscription-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .no-subscription-card p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .subjects-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #4682B4;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 2px;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 24px;
            padding: 0;
        }

        .subject-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(70, 130, 180, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 280px;
        }

        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .subject-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(70, 130, 180, 0.2);
            border-color: #4682B4;
        }

        .subject-card:hover::before {
            height: 6px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
        }

        .subject-card:active {
            transform: translateY(-2px);
            transition: all 0.1s ease;
        }

        .subject-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .subject-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
            transition: all 0.3s ease;
        }

        .subject-card:hover .subject-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
        }

        .subject-info h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin: 0 0 5px 0;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .subject-card:hover .subject-info h3 {
            color: #4682B4;
        }

        .subject-info .subject-meta {
            color: #6c757d;
            font-size: 0.9rem;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .meta-tag {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .subject-card:hover .meta-tag {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-color: #4682B4;
        }

        .subject-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .subject-actions {
            display: flex;
            gap: 12px;
            margin-top: auto;
            padding-top: 16px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
            flex: 1;
            min-height: 44px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(70, 130, 180, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: #4682B4;
            border: 2px solid #4682B4;
        }

        .btn-outline:hover {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .empty-state::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .empty-icon {
            font-size: 4rem;
            color: #87CEEB;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .empty-state h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px 20px;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
            text-align: center;
            border-top: 4px solid #4682B4;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.2);
        }

        .stat-card:hover::before {
            height: 6px;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: #4682B4;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .stat-card:hover .stat-number {
            color: #87CEEB;
            transform: scale(1.1);
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .curriculum-container {
                padding: 0 15px;
            }
        }

        @media (max-width: 1024px) {
            .subjects-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 20px;
            }

            .stats-cards {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 15px;
            }

            .subscription-details {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .curriculum-container {
                padding: 0 10px;
            }

            .curriculum-header {
                padding: 25px 15px;
                margin-bottom: 25px;
                border-radius: 12px;
            }

            .curriculum-header h1 {
                font-size: 2rem;
            }

            .curriculum-header p {
                font-size: 1rem;
            }

            .subjects-grid {
                grid-template-columns: 1fr;
                gap: 18px;
            }

            .user-info-details {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .subject-card {
                padding: 20px;
                border-radius: 14px;
            }

            .subject-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .subject-icon {
                width: 50px;
                height: 50px;
                font-size: 2rem;
                flex-shrink: 0;
            }

            .subject-actions {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                padding: 12px 16px;
                font-size: 0.9rem;
                justify-content: center;
                border-radius: 8px;
            }

            .subscription-card {
                padding: 20px;
                margin-bottom: 25px;
            }

            .subscription-details {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .subscription-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .subscription-actions {
                flex-direction: column;
                gap: 10px;
            }

            .no-subscription-card {
                padding: 20px;
                margin-bottom: 25px;
            }
        }

        @media (max-width: 480px) {
            .curriculum-container {
                padding: 0 8px;
            }

            .curriculum-header {
                padding: 20px 12px;
                border-radius: 12px;
                margin-bottom: 20px;
            }

            .curriculum-header h1 {
                font-size: 1.8rem;
            }

            .user-info-card {
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 10px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .stat-card {
                padding: 18px 15px;
                border-radius: 12px;
            }

            .stat-number {
                font-size: 1.8rem;
            }

            .subject-card {
                padding: 16px;
                border-radius: 12px;
            }

            .section-title {
                font-size: 1.5rem;
                margin-bottom: 16px;
            }

            .info-item {
                padding: 8px 10px;
                font-size: 0.9rem;
                border-radius: 8px;
            }

            .subscription-card {
                padding: 16px;
                border-radius: 12px;
            }

            .subscription-details {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .subscription-detail {
                padding: 10px;
                border-radius: 8px;
            }

            .no-subscription-card {
                padding: 16px;
                border-radius: 12px;
            }

            .no-subscription-icon {
                font-size: 2.5rem;
                margin-bottom: 12px;
            }
        }

        /* Loading Animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Accessibility Improvements */
        .subject-card:focus-within {
            outline: 2px solid #4682B4;
            outline-offset: 2px;
        }

        .btn:focus {
            outline: 2px solid #4682B4;
            outline-offset: 2px;
        }

        /* Print Styles */
        @media print {
            .curriculum-header {
                background: #4682B4 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .subject-card {
                break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .btn {
                display: none !important;
            }

            .subscription-card,
            .no-subscription-card {
                display: none !important;
            }
        }

        /* Search and Filter Styles */
        .search-filter-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .search-container {
            margin-bottom: 16px;
        }

        .search-box {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-box input {
            width: 100%;
            padding: 16px 50px 16px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .search-box input:focus {
            outline: none;
            border-color: #87CEEB;
            background: white;
            box-shadow: 0 0 0 4px rgba(135, 206, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 18px;
        }

        .clear-search {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .clear-search:hover {
            background: #e9ecef;
            color: #495057;
        }

        .filter-results {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        #results-count {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
        }

        .reset-filters {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .reset-filters:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        /* Enhanced Focus States for Accessibility */
        .subject-card:focus-within {
            outline: 3px solid #4682B4;
            outline-offset: 2px;
            transform: translateY(-5px);
        }

        .btn:focus {
            outline: 3px solid #4682B4;
            outline-offset: 2px;
        }

        /* Enhanced Mobile Touch Interactions */
        @media (hover: none) and (pointer: coarse) {
            .subject-card:active {
                transform: translateY(-2px) scale(0.98);
                transition: all 0.1s ease;
            }

            .btn:active {
                transform: translateY(0) scale(0.95);
                transition: all 0.1s ease;
            }

            .subscription-card:active {
                transform: translateY(-1px) scale(0.99);
                transition: all 0.1s ease;
            }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }

        /* Enhanced loading states */
        .subject-card.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .subject-card.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="dashboard-layout">
        <!-- Include Header -->

        <!-- Include Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->

        <!-- Main Content -->
        <main class="dashboard-main">

            <div class="curriculum-container">
                <!-- Header Section -->
                <div class="curriculum-header">
                    <h1><i class="fas fa-graduation-cap"></i> المنهج الدراسي</h1>
                    <p>استكشف جميع المواد والأقسام المتاحة لك</p>
                </div>

                <!-- Search Section -->
                <div class="search-filter-section">
                    <div class="search-container">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="subjectSearch" placeholder="ابحث عن مادة أو قسم..." onkeyup="filterSubjects()">
                            <button class="clear-search" onclick="clearSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-results">
                        <span id="results-count">عرض جميع الأقسام</span>
                        <button class="reset-filters" onclick="resetFilters()" style="display: none;">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php else: ?>
                    


                    <!-- User Info Card -->
                    <div class="user-info-card">
                        <h3><i class="fas fa-user-graduate"></i> معلومات الطالب</h3>
                        <div class="user-info-details">
                            <div class="info-item">
                                <span class="icon">👤</span>
                                <span><?php echo htmlspecialchars($user['name']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="icon">🎓</span>
                                <span><?php echo EDUCATION_LEVELS[$user['education_level']] ?? $user['education_level']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="icon">🏫</span>
                                <span><?php echo EDUCATION_TYPES[$user['education_type']] ?? $user['education_type']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="icon">📚</span>
                                <span>الصف <?php echo $user['grade']; ?></span>
                            </div>
                            <?php if ($user['education_level'] === 'secondary' && !empty($user['specialization'])): ?>
                                <div class="info-item">
                                    <span class="icon">🔬</span>
                                    <span><?php echo SECONDARY_SPECIALIZATIONS[$user['specialization']] ?? $user['specialization']; ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $curriculumStats['total_subjects'] ?? 0; ?></div>
                            <div class="stat-label">إجمالي المواد المتاحة</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $curriculumStats['general_subjects'] ?? 0; ?></div>
                            <div class="stat-label">مواد عامة</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $curriculumStats['level_subjects'] ?? 0; ?></div>
                            <div class="stat-label">مواد متخصصة</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $currentSubscription ? '✓' : '✗'; ?></div>
                            <div class="stat-label">حالة الاشتراك</div>
                        </div>
                    </div>

                    <?php if (empty($subjects)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">📚</div>
                            <h3>لا توجد مواد متاحة</h3>
                            <p>لم يتم إضافة مواد دراسية لمستواك التعليمي بعد</p>
                            <a href="<?php echo SITE_URL; ?>/page/courses.php" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                تصفح الكورسات
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- Subjects by Level -->
                        <?php foreach ($groupedSubjects as $level => $levelSubjects): ?>
                            <div class="subjects-section">
                                <h2 class="section-title">
                                    <i class="fas fa-book-open"></i>
                                    <?php
                                    if ($level === 'all') {
                                        echo 'مواد عامة لجميع المراحل';
                                    } else {
                                        echo 'مواد ' . (EDUCATION_LEVELS[$level] ?? $level);
                                    }
                                    ?>
                                </h2>

                                <div class="subjects-grid">
                                    <?php foreach ($levelSubjects as $subject): ?>
                                        <div class="subject-card" data-subject-id="<?php echo $subject['id']; ?>">
                                            <div class="subject-header">
                                                <div class="subject-icon">
                                                    <?php echo $subject['icon']; ?>
                                                </div>
                                                <div class="subject-info">
                                                    <h3><?php echo htmlspecialchars($subject['name']); ?></h3>
                                                    <div class="subject-meta">
                                                        <span class="meta-tag"><?php echo EDUCATION_LEVELS[$subject['education_level']] ?? $subject['education_level']; ?></span>
                                                        <span class="meta-tag"><?php echo EDUCATION_TYPES[$subject['education_type']] ?? $subject['education_type']; ?></span>
                                                        <span class="meta-tag">الصف <?php echo $subject['grade']; ?></span>
                                                        <?php if ($subject['specialization'] !== 'all'): ?>
                                                            <span class="meta-tag"><?php echo SECONDARY_SPECIALIZATIONS[$subject['specialization']] ?? $subject['specialization']; ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <?php if (!empty($subject['description'])): ?>
                                                <div class="subject-description">
                                                    <?php echo htmlspecialchars($subject['description']); ?>
                                                </div>
                                            <?php endif; ?>

                                            <div class="subject-actions">
                                                <a href="<?php echo SITE_URL; ?>/page/subject_lessons.php?subject_id=<?php echo $subject['id']; ?>"
                                                   class="btn btn-primary">
                                                    <i class="fas fa-play"></i>
                                                    بدء الدراسة
                                                </a>
                                                <a href="<?php echo SITE_URL; ?>/page/subject_details.php?subject_id=<?php echo $subject['id']; ?>"
                                                   class="btn btn-outline">
                                                    <i class="fas fa-info-circle"></i>
                                                    التفاصيل
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Include Dashboard JavaScript -->
    <script src="<?php echo SITE_URL; ?>/js/dashboard.js"></script>

    <script>
        // Enhanced interactive effects with dashboard-style animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on scroll with staggered effect
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100); // Staggered animation
                    }
                });
            }, observerOptions);

            // Observe all subject cards with initial setup
            document.querySelectorAll('.subject-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                observer.observe(card);
            });

            // Animate stats cards
            document.querySelectorAll('.stat-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });

            // Animate user info card and subscription card
            const animateCards = ['.user-info-card', '.subscription-card', '.no-subscription-card'];
            animateCards.forEach((selector, index) => {
                const card = document.querySelector(selector);
                if (card) {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 200 + (index * 100));
                }
            });

            // Enhanced click effect to cards
            document.querySelectorAll('.subject-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('.btn')) {
                        // Add ripple effect
                        const ripple = document.createElement('div');
                        ripple.style.cssText = `
                            position: absolute;
                            border-radius: 50%;
                            background: rgba(70, 130, 180, 0.3);
                            transform: scale(0);
                            animation: ripple 0.6s linear;
                            pointer-events: none;
                        `;

                        const rect = this.getBoundingClientRect();
                        const size = Math.max(rect.width, rect.height);
                        ripple.style.width = ripple.style.height = size + 'px';
                        ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                        ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                        this.appendChild(ripple);

                        setTimeout(() => {
                            const primaryBtn = this.querySelector('.btn-primary');
                            if (primaryBtn) {
                                primaryBtn.click();
                            }
                            ripple.remove();
                        }, 300);
                    }
                });

                // Add hover sound effect (optional)
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add CSS for ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                .subject-card {
                    position: relative;
                    overflow: hidden;
                }
            `;
            document.head.appendChild(style);

            // Parallax effect for header (disabled on mobile for performance)
            if (window.innerWidth > 768) {
                window.addEventListener('scroll', function() {
                    const header = document.querySelector('.curriculum-header');
                    if (header) {
                        const scrolled = window.pageYOffset;
                        const rate = scrolled * -0.3;
                        header.style.transform = `translateY(${rate}px)`;
                    }
                });
            }

            // Add keyboard navigation support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    const focusedCard = document.activeElement.closest('.subject-card');
                    if (focusedCard) {
                        e.preventDefault();
                        const primaryBtn = focusedCard.querySelector('.btn-primary');
                        if (primaryBtn) {
                            primaryBtn.click();
                        }
                    }
                }
            });

            // Add loading states for better UX
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.href) {
                        this.style.opacity = '0.7';
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                    }
                });
            });

            // Initialize search functionality
            updateResultsCount();
        });

        // Search and Filter Functions
        function filterSubjects() {
            const searchTerm = document.getElementById('subjectSearch').value.toLowerCase();
            const subjectCards = document.querySelectorAll('.subject-card');
            let visibleCount = 0;

            subjectCards.forEach(card => {
                const title = card.querySelector('h3') ? card.querySelector('h3').textContent.toLowerCase() : '';
                const description = card.querySelector('p') ? card.querySelector('p').textContent.toLowerCase() : '';

                const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);

                if (matchesSearch) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Update results count
            document.getElementById('results-count').textContent =
                searchTerm ? `عرض ${visibleCount} من ${subjectCards.length} قسم` : 'عرض جميع الأقسام';

            // Show/hide clear and reset buttons
            document.querySelector('.clear-search').style.display = searchTerm ? 'block' : 'none';
            document.querySelector('.reset-filters').style.display = searchTerm ? 'block' : 'none';
        }

        function clearSearch() {
            document.getElementById('subjectSearch').value = '';
            filterSubjects();
        }

        function resetFilters() {
            clearSearch();
        }

        function updateResultsCount() {
            const totalCards = document.querySelectorAll('.subject-card').length;
            document.getElementById('results-count').textContent = `عرض جميع الأقسام (${totalCards})`;
        }

        // Add search input event listener
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('subjectSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const clearBtn = document.querySelector('.clear-search');
                    clearBtn.style.display = this.value ? 'block' : 'none';
                });
            }
        });
    </script>
    <script src="<?php echo SITE_URL; ?>/js/enhanced-ui.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/loading-system.js"></script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
