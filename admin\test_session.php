<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>اختبار جلسة الإدارة</h2>";

echo "<h3>بيانات الجلسة:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

if (isset($_SESSION['admin_id'])) {
    echo "<h3>✅ الإدارة مسجلة دخول</h3>";
    echo "معرف الإدارة: " . $_SESSION['admin_id'] . "<br>";
    echo "اسم المستخدم: " . ($_SESSION['admin_username'] ?? 'غير محدد') . "<br>";
    echo "الاسم الكامل: " . ($_SESSION['admin_name'] ?? 'غير محدد') . "<br>";
    echo "الدور: " . ($_SESSION['admin_role'] ?? 'غير محدد') . "<br>";
    
    // Test search users API
    echo "<h3>اختبار API البحث عن المستخدمين</h3>";
    $testQuery = "test";
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../api/search_users.php?q=' . urlencode($testQuery);
    echo "URL: " . $url . "<br>";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . $_SERVER['HTTP_COOKIE']
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    echo "الاستجابة: <pre>" . htmlspecialchars($response) . "</pre><br>";
    
    // Test getting all users
    echo "<h3>اختبار الحصول على جميع المستخدمين</h3>";
    try {
        $userManager = new UserManager();
        $allUsers = $userManager->getAllActiveUsers();
        echo "عدد المستخدمين النشطين: " . count($allUsers) . "<br>";
        
        if (!empty($allUsers)) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الاسم</th></tr>";
            foreach (array_slice($allUsers, 0, 5) as $user) { // Show first 5 users
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . htmlspecialchars($user['first_name'] . ' ' . $user['second_name']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "خطأ: " . $e->getMessage() . "<br>";
    }
    
} else {
    echo "<h3>❌ الإدارة غير مسجلة دخول</h3>";
    echo "<a href=\"" . SITE_URL . "/admin/login.php\">تسجيل دخول الإدارة</a>";
}

echo "<br><br><a href='send_notifications.php'>صفحة إرسال الإشعارات</a>";
?>
