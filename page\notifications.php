<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userManager = new UserManager();
$userData = $userManager->getUserById($_SESSION['user_id']);
$notifications = $userManager->getUserNotifications($_SESSION['user_id'], 50); // Get more notifications

// Mark all as read when viewing this page
$userManager->markAllNotificationsAsRead($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/notifications.css">
    <style>
        .notifications-page {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.3);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .notifications-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            overflow: hidden;
        }
        
        .notification-item-full {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .notification-item-full:hover {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
        }
        
        .notification-item-full:last-child {
            border-bottom: none;
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .notification-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .notification-date {
            font-size: 12px;
            color: #999;
            background: rgba(70, 130, 180, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
        }
        
        .notification-message {
            color: #555;
            line-height: 1.6;
            font-size: 15px;
            margin: 0;
        }
        
        .notification-type-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 4px;
            height: 60px;
            border-radius: 2px;
        }
        
        .notification-type-badge.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .notification-type-badge.error {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        }
        
        .notification-type-badge.warning {
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
        }
        
        .notification-type-badge.info {
            background: linear-gradient(135deg, #4682B4 0%, #3498db 100%);
        }
        
        .empty-notifications {
            text-align: center;
            padding: 60px 30px;
            color: #999;
        }
        
        .empty-notifications::before {
            content: '🔔';
            font-size: 64px;
            display: block;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
            margin-bottom: 20px;
        }
        
        .back-button:hover {
            background: linear-gradient(135deg, #4682B4 0%, #2c5282 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
        }
        
        @media (max-width: 768px) {
            .notifications-page {
                padding: 15px;
            }
            
            .page-header {
                padding: 20px;
                margin-bottom: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .notification-item-full {
                padding: 20px;
            }
            
            .notification-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
            
            .notification-title {
                font-size: 16px;
            }
        }
    </style>

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="notifications-page">
        <a href="<?php echo SITE_URL; ?>/page/dashboard.php" class="back-button">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            العودة إلى الداشبورد
        </a>
        
        <div class="page-header">
            <h1>جميع الإشعارات</h1>
            <p>عرض جميع الإشعارات والرسائل المرسلة إليك</p>
        </div>
        
        <div class="notifications-container">
            <?php if (empty($notifications)): ?>
                <div class="empty-notifications">
                    <h3>لا توجد إشعارات</h3>
                    <p>لم تتلق أي إشعارات حتى الآن</p>
                </div>
            <?php else: ?>
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item-full">
                        <div class="notification-type-badge <?php echo htmlspecialchars($notification['type']); ?>"></div>
                        <div class="notification-header">
                            <h3 class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></h3>
                            <span class="notification-date"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></span>
                        </div>
                        <p class="notification-message"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="<?php echo SITE_URL; ?>/js/notifications.js"></script>
    <script>
        // Show success message that all notifications are marked as read
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!empty($notifications)): ?>
                notifications.success('تم تحديد جميع الإشعارات كمقروءة');
            <?php endif; ?>
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
