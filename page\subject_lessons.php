<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get subject ID
$subject_id = isset($_GET['subject_id']) ? (int)$_GET['subject_id'] : 0;

if (!$subject_id) {
    header('Location: curriculum.php');
    exit;
}

// Get subject info
$stmt = $db->prepare("SELECT * FROM curriculum_subjects WHERE id = ? AND is_active = 1");
$stmt->execute([$subject_id]);
$subject = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$subject) {
    header('Location: curriculum.php');
    exit;
}

// Get user's subscription information
$user_id = $_SESSION['user_id'];
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date, u.current_plan_id,
                     sp.name as plan_name, sp.icon as plan_icon, sp.color as plan_color
                     FROM users u
                     LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                     WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

// Get lessons for this subject
try {
    $lessons_query = "
        SELECT l.*,
               COALESCE(ulp.completion_percentage, 0) as completion_percentage,
               COALESCE(ulp.is_completed, 0) as is_completed,
               (SELECT COUNT(*) FROM lesson_videos WHERE lesson_id = l.id AND is_active = 1) as video_count,
               (SELECT COUNT(*) FROM lesson_exercises WHERE lesson_id = l.id AND is_active = 1) as exercise_count,
               (SELECT COUNT(*) FROM lesson_exams WHERE lesson_id = l.id AND is_active = 1) as exam_count,
               (SELECT COUNT(*) FROM lesson_summaries WHERE lesson_id = l.id AND is_active = 1) as summary_count
        FROM lessons l
        LEFT JOIN user_lesson_progress ulp ON l.id = ulp.lesson_id AND ulp.user_id = ?
        WHERE l.subject_id = ? AND l.is_active = 1
        ORDER BY l.sort_order, l.lesson_number
    ";
    $stmt = $db->prepare($lessons_query);
    $stmt->execute([$user_id, $subject_id]);
    $lessons = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $lessons = [];
}

// Calculate overall progress
$total_lessons = count($lessons);
$completed_lessons = 0;
$total_progress = 0;

foreach ($lessons as $lesson) {
    if ($lesson['is_completed']) {
        $completed_lessons++;
    }
    $total_progress += $lesson['completion_percentage'];
}

$overall_progress = $total_lessons > 0 ? round($total_progress / $total_lessons, 2) : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($subject['name']); ?> - دروس القسم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="page-container">
                <!-- Subject Header -->
                <div class="subject-header">
                    <div class="subject-info">
                        <div class="subject-icon-large" style="background: linear-gradient(135deg, <?php echo $subject['color']; ?>20, <?php echo $subject['color']; ?>40); color: <?php echo $subject['color']; ?>;">
                            <?php echo $subject['icon']; ?>
                        </div>
                        <div class="subject-details">
                            <h1><?php echo htmlspecialchars($subject['name']); ?></h1>
                            <?php if ($subject['name_en']): ?>
                                <p class="subject-name-en"><?php echo htmlspecialchars($subject['name_en']); ?></p>
                            <?php endif; ?>
                            <?php if ($subject['description']): ?>
                                <p class="subject-description"><?php echo htmlspecialchars($subject['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="subject-actions">
                        <a href="curriculum.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للمنهج
                        </a>
                    </div>
                </div>

                <!-- Progress Overview -->
                <div class="progress-overview">
                    <div class="progress-card">
                        <div class="progress-icon total">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="progress-info">
                            <h3>إجمالي الدروس</h3>
                            <span class="progress-number"><?php echo $total_lessons; ?></span>
                            <span class="progress-subtitle">درس متاح</span>
                        </div>
                    </div>
                    <div class="progress-card">
                        <div class="progress-icon completed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="progress-info">
                            <h3>الدروس المكتملة</h3>
                            <span class="progress-number"><?php echo $completed_lessons; ?></span>
                            <span class="progress-subtitle">درس مكتمل</span>
                        </div>
                    </div>
                    <div class="progress-card">
                        <div class="progress-icon percentage">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="progress-info">
                            <h3>نسبة التقدم</h3>
                            <span class="progress-number"><?php echo $overall_progress; ?>%</span>
                            <span class="progress-subtitle">من إجمالي القسم</span>
                        </div>
                    </div>
                    <div class="progress-card">
                        <div class="progress-icon available">
                            <i class="fas fa-unlock"></i>
                        </div>
                        <div class="progress-info">
                            <h3>الدروس المتاحة</h3>
                            <span class="progress-number" id="available-lessons">
                                <?php 
                                $available = 0;
                                foreach ($lessons as $lesson) {
                                    if ($lesson['is_free'] || $has_subscription) {
                                        $available++;
                                    }
                                }
                                echo $available;
                                ?>
                            </span>
                            <span class="progress-subtitle">درس يمكن الوصول إليه</span>
                        </div>
                    </div>
                </div>

                <!-- Lessons Grid -->
                <div class="lessons-grid">
                    <?php foreach ($lessons as $lesson): ?>
                        <?php 
                        $is_accessible = $lesson['is_free'] || $has_subscription;
                        $completion_percentage = $lesson['completion_percentage'];
                        ?>
                        <div class="lesson-card <?php echo $is_accessible ? 'accessible' : 'locked'; ?> <?php echo $lesson['is_completed'] ? 'completed' : ''; ?>" 
                             data-lesson-id="<?php echo $lesson['id']; ?>">
                            
                            <!-- Lesson Header -->
                            <div class="lesson-header">
                                <div class="lesson-number">
                                    <span><?php echo $lesson['lesson_number']; ?></span>
                                </div>
                                <div class="lesson-status">
                                    <?php if ($lesson['is_completed']): ?>
                                        <div class="completion-circle completed">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    <?php else: ?>
                                        <div class="completion-circle" style="background: conic-gradient(<?php echo $subject['color']; ?> <?php echo $completion_percentage * 3.6; ?>deg, #e9ecef 0deg);">
                                            <span><?php echo round($completion_percentage); ?>%</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Lesson Content -->
                            <div class="lesson-content">
                                <h3 class="lesson-title"><?php echo htmlspecialchars($lesson['title']); ?></h3>
                                <?php if ($lesson['description']): ?>
                                    <p class="lesson-description"><?php echo htmlspecialchars(substr($lesson['description'], 0, 100)) . '...'; ?></p>
                                <?php endif; ?>

                                <!-- Content Stats -->
                                <div class="content-stats">
                                    <?php if ($lesson['video_count'] > 0): ?>
                                        <div class="stat-item">
                                            <i class="fas fa-video"></i>
                                            <span><?php echo $lesson['video_count']; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($lesson['exercise_count'] > 0): ?>
                                        <div class="stat-item">
                                            <i class="fas fa-dumbbell"></i>
                                            <span><?php echo $lesson['exercise_count']; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($lesson['exam_count'] > 0): ?>
                                        <div class="stat-item">
                                            <i class="fas fa-clipboard-check"></i>
                                            <span><?php echo $lesson['exam_count']; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($lesson['summary_count'] > 0): ?>
                                        <div class="stat-item">
                                            <i class="fas fa-file-pdf"></i>
                                            <span><?php echo $lesson['summary_count']; ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Lesson Footer -->
                            <div class="lesson-footer">
                                <?php if ($is_accessible): ?>
                                    <a href="lesson_content.php?lesson_id=<?php echo $lesson['id']; ?>" class="btn btn-primary lesson-btn">
                                        <i class="fas fa-play"></i>
                                        <?php echo $lesson['is_completed'] ? 'مراجعة الدرس' : 'بدء الدرس'; ?>
                                    </a>
                                <?php else: ?>
                                    <button class="btn btn-locked lesson-btn" onclick="showSubscriptionModal()">
                                        <i class="fas fa-lock"></i>
                                        درس مدفوع
                                    </button>
                                <?php endif; ?>
                                
                                <div class="lesson-type">
                                    <?php if ($lesson['is_free']): ?>
                                        <span class="badge badge-success">مجاني</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">مدفوع</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($lessons)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📚</div>
                        <h2>لا توجد دروس متاحة</h2>
                        <p>لم يتم إضافة دروس لهذا القسم بعد</p>
                        <a href="curriculum.php" class="btn btn-primary">العودة للمنهج</a>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Include Subscription Modal -->
    <?php include __DIR__ . '/../includes/subscription_modal.php'; ?>

    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(135, 206, 235, 0.02) 0%,
                rgba(70, 130, 180, 0.05) 50%,
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .subject-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .subject-info {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .subject-icon-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(70, 130, 180, 0.2);
        }

        .subject-details h1 {
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 2.2rem;
            font-weight: 700;
        }

        .subject-name-en {
            color: #6c757d;
            font-size: 16px;
            margin: 0 0 10px 0;
            font-style: italic;
        }

        .subject-description {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
        }

        .progress-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .progress-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.4s ease;
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .progress-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .progress-icon {
            font-size: 1.8rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
        }

        .progress-icon.total {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .progress-icon.completed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .progress-icon.percentage {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }

        .progress-icon.available {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        }

        .progress-info h3 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }

        .progress-number {
            font-size: 2.2rem;
            font-weight: bold;
            color: #4682B4;
            display: block;
            line-height: 1;
        }

        .progress-subtitle {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            display: block;
        }

        .lessons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .lesson-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            border: 1px solid rgba(135, 206, 235, 0.2);
            position: relative;
        }

        .lesson-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .lesson-card.locked {
            opacity: 0.7;
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .lesson-card.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .lesson-header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(135, 206, 235, 0.1);
        }

        .lesson-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .completion-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #666;
            position: relative;
        }

        .completion-circle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: white;
            border-radius: 50%;
            z-index: 1;
        }

        .completion-circle span {
            position: relative;
            z-index: 2;
        }

        .completion-circle.completed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .completion-circle.completed::before {
            display: none;
        }

        .lesson-content {
            padding: 20px;
        }

        .lesson-title {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 10px 0;
            line-height: 1.3;
        }

        .lesson-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin: 0 0 15px 0;
        }

        .content-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #6c757d;
            font-size: 12px;
        }

        .stat-item i {
            color: #4682B4;
        }

        .lesson-footer {
            padding: 20px;
            border-top: 1px solid rgba(135, 206, 235, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lesson-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-locked {
            background: #6c757d;
            color: white;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .lesson-type {
            display: flex;
            gap: 5px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-state h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 16px;
        }



        @media (max-width: 768px) {
            .subject-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .subject-info {
                flex-direction: column;
                text-align: center;
            }

            .progress-overview {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .lessons-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .lesson-header {
                padding: 15px;
            }

            .lesson-content {
                padding: 15px;
            }

            .lesson-footer {
                padding: 15px;
                flex-direction: column;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .progress-overview {
                grid-template-columns: 1fr;
            }

            .progress-card {
                padding: 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .progress-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .progress-number {
                font-size: 1.8rem;
            }
        }
    </style>

    <script>
        // Additional page-specific JavaScript can go here
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
