<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$db = Database::getInstance()->getConnection();
$user_id = $_SESSION['user_id'];

// Get exam ID
$exam_id = isset($_GET['exam_id']) ? (int)$_GET['exam_id'] : 0;

if (!$exam_id) {
    header('Location: curriculum.php');
    exit;
}

// Handle reset parameter
if (isset($_GET['reset']) && $_GET['reset'] == '1') {
    unset($_SESSION['exam_results_' . $exam_id]);
    header('Location: exam.php?exam_id=' . $exam_id);
    exit;
}

// Get exam info with lesson and subject
$stmt = $db->prepare("
    SELECT le.*, l.title as lesson_title, l.id as lesson_id, l.subject_id, l.is_free,
           cs.name as subject_name, cs.color as subject_color
    FROM lesson_exams le
    JOIN lessons l ON le.lesson_id = l.id
    JOIN curriculum_subjects cs ON l.subject_id = cs.id
    WHERE le.id = ? AND le.is_active = 1 AND l.is_active = 1
");
$stmt->execute([$exam_id]);
$exam = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$exam) {
    header('Location: curriculum.php');
    exit;
}

// Check if user can access this exam
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date
                     FROM users u WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

$can_access = $exam['is_free'] || $has_subscription;

if (!$can_access) {
    header('Location: lesson_content.php?lesson_id=' . $exam['lesson_id']);
    exit;
}

// Get exam questions
try {
    $questions_stmt = $db->prepare("SELECT * FROM exam_questions WHERE exam_id = ? AND is_active = 1 ORDER BY question_order, id");
    $questions_stmt->execute([$exam_id]);
    $questions = $questions_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $questions = [];
}

// Calculate total points
$total_points = array_sum(array_column($questions, 'points'));

// Get user's exam progress
try {
    $stmt = $db->prepare("SELECT * FROM user_exam_progress WHERE user_id = ? AND exam_id = ?");
    $stmt->execute([$user_id, $exam_id]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $progress = null;
}

// Initialize variables
$exam_completed = false;
$score = 0;
$is_passed = false;
$earned_points = 0;
$correct_answers = 0;
$time_taken_minutes = 0;
$results = [];
$show_results = false;
$passing_score = 60; // Default passing score

// Check if we have fresh results from form submission
if (isset($_SESSION['exam_results_' . $exam_id])) {
    $session_results = $_SESSION['exam_results_' . $exam_id];
    $exam_completed = true;
    $score = $session_results['score'];
    $is_passed = $session_results['is_passed'];
    $earned_points = $session_results['earned_points'];
    $correct_answers = $session_results['correct_answers'];
    $time_taken_minutes = $session_results['time_taken_minutes'];
    $results = $session_results['results'];
    $show_results = true;
    
    // Clear session after displaying
    unset($_SESSION['exam_results_' . $exam_id]);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_exam']) && !$exam_completed) {
    $answers = $_POST['answers'] ?? [];
    $start_time = $_POST['start_time'] ?? time();
    $time_taken = time() - $start_time;
    $time_taken_minutes = round($time_taken / 60, 2);
    
    $earned_points = 0;
    $correct_answers = 0;
    $results = [];
    
    // Calculate score and prepare results
    foreach ($questions as $question) {
        $user_answer = $answers[$question['id']] ?? '';
        $is_correct = $user_answer === $question['correct_answer'];
        
        if ($is_correct) {
            $earned_points += $question['points'];
            $correct_answers++;
        }
        
        // Store result for each question
        $results[] = [
            'question' => $question,
            'user_answer' => $user_answer,
            'is_correct' => $is_correct
        ];
    }
    
    $score = $total_points > 0 ? round(($earned_points / $total_points) * 100, 2) : 0;
    $is_passed = $score >= $passing_score;
    
    try {
        // Save or update progress
        if ($progress) {
            $stmt = $db->prepare("UPDATE user_exam_progress SET is_completed = 1, score = ?, is_passed = ?, time_taken_minutes = ?, completed_at = NOW() WHERE user_id = ? AND exam_id = ?");
            $stmt->execute([$score, $is_passed, $time_taken_minutes, $user_id, $exam_id]);
        } else {
            $stmt = $db->prepare("INSERT INTO user_exam_progress (user_id, exam_id, is_completed, score, is_passed, time_taken_minutes, completed_at) VALUES (?, ?, 1, ?, ?, ?, NOW())");
            $stmt->execute([$user_id, $exam_id, $score, $is_passed, $time_taken_minutes]);
        }
        
        // Update lesson progress
        updateLessonProgress($db, $user_id, $exam['lesson_id']);
        
        // Store results in session for display
        $_SESSION['exam_results_' . $exam_id] = [
            'score' => $score,
            'is_passed' => $is_passed,
            'earned_points' => $earned_points,
            'correct_answers' => $correct_answers,
            'time_taken_minutes' => $time_taken_minutes,
            'results' => $results
        ];
        
        // Redirect to show results
        header('Location: exam.php?exam_id=' . $exam_id);
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في حفظ النتيجة: " . $e->getMessage();
    }
}

function updateLessonProgress($db, $user_id, $lesson_id) {
    // Get total content count
    $total_query = "
        SELECT 
            (SELECT COUNT(*) FROM lesson_videos WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exercises WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exams WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_summaries WHERE lesson_id = ? AND is_active = 1) as total_count
    ";
    $stmt = $db->prepare($total_query);
    $stmt->execute([$lesson_id, $lesson_id, $lesson_id, $lesson_id]);
    $total_count = $stmt->fetchColumn();
    
    // Get completed content count
    try {
        $completed_query = "
            SELECT 
                (SELECT COUNT(*) FROM user_video_progress uvp 
                 JOIN lesson_videos lv ON uvp.video_id = lv.id 
                 WHERE uvp.user_id = ? AND lv.lesson_id = ? AND uvp.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exercise_progress uep 
                 JOIN lesson_exercises le ON uep.exercise_id = le.id 
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exam_progress uep 
                 JOIN lesson_exams le ON uep.exam_id = le.id 
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_summary_progress usp 
                 JOIN lesson_summaries ls ON usp.summary_id = ls.id 
                 WHERE usp.user_id = ? AND ls.lesson_id = ? AND usp.is_completed = 1) as completed_count
        ";
        $stmt = $db->prepare($completed_query);
        $stmt->execute([$user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id]);
        $completed_count = $stmt->fetchColumn();
    } catch (Exception $e) {
        $completed_count = 0;
    }
    
    $completion_percentage = $total_count > 0 ? round(($completed_count / $total_count) * 100, 2) : 0;
    $is_completed = $completion_percentage >= 100;
    
    // Update or insert lesson progress
    try {
        $stmt = $db->prepare("
            INSERT INTO user_lesson_progress (user_id, lesson_id, completion_percentage, is_completed, last_accessed_at, completed_at)
            VALUES (?, ?, ?, ?, NOW(), ?)
            ON DUPLICATE KEY UPDATE 
                completion_percentage = VALUES(completion_percentage),
                is_completed = VALUES(is_completed),
                last_accessed_at = VALUES(last_accessed_at),
                completed_at = CASE WHEN VALUES(is_completed) = 1 AND is_completed = 0 THEN NOW() ELSE completed_at END
        ");
        $stmt->execute([$user_id, $lesson_id, $completion_percentage, $is_completed, $is_completed ? date('Y-m-d H:i:s') : null]);
    } catch (Exception $e) {
        // Ignore errors for now
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($exam['title']); ?> - امتحان - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="exam-container">
                <!-- Exam Header -->
                <div class="exam-header">
                    <div class="exam-info">
                        <div class="exam-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="exam-details">
                            <h1><?php echo htmlspecialchars($exam['title']); ?></h1>
                            <div class="exam-meta">
                                <span class="lesson-badge">
                                    <?php echo htmlspecialchars($exam['lesson_title']); ?>
                                </span>
                                <span class="subject-badge" style="background-color: <?php echo $exam['subject_color']; ?>20; color: <?php echo $exam['subject_color']; ?>;">
                                    <?php echo htmlspecialchars($exam['subject_name']); ?>
                                </span>
                                <span class="duration-badge">
                                    <i class="fas fa-clock"></i>
                                    <?php echo $exam['duration_minutes']; ?> دقيقة
                                </span>
                                <span class="passing-badge">
                                    <i class="fas fa-percentage"></i>
                                    <?php echo $passing_score; ?>% للنجاح
                                </span>
                                <?php if ($progress && $progress['is_completed']): ?>
                                    <span class="completion-badge <?php echo $progress['is_passed'] ? 'passed' : 'failed'; ?>">
                                        <i class="fas fa-<?php echo $progress['is_passed'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                        <?php echo $progress['is_passed'] ? 'نجح' : 'لم ينجح'; ?> - <?php echo round($progress['score'], 1); ?>%
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php if ($exam['description']): ?>
                                <p class="exam-description"><?php echo htmlspecialchars($exam['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="exam-actions">
                        <a href="lesson_content.php?lesson_id=<?php echo $exam['lesson_id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للدرس
                        </a>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($exam_completed && $show_results): ?>
                    <!-- Results Section -->
                    <div class="result-card <?php echo $is_passed ? 'passed' : 'failed'; ?>">
                        <div class="result-icon">
                            <i class="fas fa-<?php echo $is_passed ? 'trophy' : 'times-circle'; ?>"></i>
                        </div>
                        <div class="result-content">
                            <h2><?php echo $is_passed ? 'مبروك! لقد نجحت' : 'للأسف لم تنجح'; ?></h2>
                            <div class="score-display">
                                <span class="score-number"><?php echo round($score, 1); ?>%</span>
                                <span class="score-label">النتيجة النهائية</span>
                            </div>
                            <div class="score-details">
                                <div class="detail-item">
                                    <span class="detail-label">النقاط المكتسبة:</span>
                                    <span class="detail-value"><?php echo $earned_points; ?> من <?php echo $total_points; ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">الإجابات الصحيحة:</span>
                                    <span class="detail-value"><?php echo $correct_answers; ?> من <?php echo count($questions); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">الوقت المستغرق:</span>
                                    <span class="detail-value"><?php echo round($time_taken_minutes, 1); ?> دقيقة</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">الدرجة المطلوبة للنجاح:</span>
                                    <span class="detail-value"><?php echo $passing_score; ?>%</span>
                                </div>
                            </div>
                            <div class="result-actions">
                                <a href="lesson_content.php?lesson_id=<?php echo $exam['lesson_id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للدرس
                                </a>
                                <?php if (!$is_passed): ?>
                                    <a href="exam.php?exam_id=<?php echo $exam_id; ?>&reset=1" class="btn btn-warning">
                                        <i class="fas fa-redo"></i>
                                        إعادة الامتحان
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Results -->
                    <?php if (!empty($results)): ?>
                    <div class="detailed-results">
                        <div class="results-header">
                            <h3><i class="fas fa-list-check"></i> مراجعة الإجابات</h3>
                        </div>
                        <div class="results-container">
                            <?php foreach ($results as $index => $result): ?>
                                <div class="result-question <?php echo $result['is_correct'] ? 'correct' : 'incorrect'; ?>">
                                    <div class="result-question-header">
                                        <div class="question-info">
                                            <span class="question-number">السؤال <?php echo $index + 1; ?></span>
                                            <div class="question-meta">
                                                <span class="result-status">
                                                    <?php if ($result['is_correct']): ?>
                                                        <i class="fas fa-check-circle"></i>
                                                        إجابة صحيحة
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle"></i>
                                                        إجابة خاطئة
                                                    <?php endif; ?>
                                                </span>
                                                <span class="question-points">
                                                    <i class="fas fa-star"></i>
                                                    <?php echo $result['question']['points']; ?> نقطة
                                                    <?php if ($result['is_correct']): ?>
                                                        <span class="earned-points">(مكتسبة)</span>
                                                    <?php else: ?>
                                                        <span class="lost-points">(مفقودة)</span>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="result-question-content">
                                        <h4><?php echo htmlspecialchars($result['question']['question_text']); ?></h4>

                                        <?php if ($result['question']['question_type'] === 'true_false'): ?>
                                            <div class="answer-review">
                                                <div class="answer-item user-answer <?php echo $result['is_correct'] ? 'correct' : 'incorrect'; ?>">
                                                    <span class="answer-label">إجابتك:</span>
                                                    <span class="answer-value"><?php echo $result['user_answer'] === 'true' ? 'صح' : 'خطأ'; ?></span>
                                                </div>
                                                <?php if (!$result['is_correct']): ?>
                                                    <div class="answer-item correct-answer">
                                                        <span class="answer-label">الإجابة الصحيحة:</span>
                                                        <span class="answer-value"><?php echo $result['question']['correct_answer'] === 'true' ? 'صح' : 'خطأ'; ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <?php
                                            $options = json_decode($result['question']['options'], true);
                                            $option_labels = ['أ', 'ب', 'ج', 'د'];
                                            $option_keys = ['option_a', 'option_b', 'option_c', 'option_d'];
                                            ?>
                                            <div class="options-review">
                                                <?php foreach ($option_keys as $i => $key): ?>
                                                    <?php if (!empty($options[$key])): ?>
                                                        <div class="option-review <?php
                                                            if ($key === $result['question']['correct_answer']) echo 'correct-option';
                                                            elseif ($key === $result['user_answer'] && !$result['is_correct']) echo 'user-wrong-option';
                                                            elseif ($key === $result['user_answer'] && $result['is_correct']) echo 'user-correct-option';
                                                        ?>">
                                                            <span class="option-letter"><?php echo $option_labels[$i]; ?>)</span>
                                                            <span class="option-text"><?php echo htmlspecialchars($options[$key]); ?></span>
                                                            <?php if ($key === $result['question']['correct_answer']): ?>
                                                                <i class="fas fa-check-circle option-icon"></i>
                                                            <?php elseif ($key === $result['user_answer'] && !$result['is_correct']): ?>
                                                                <i class="fas fa-times-circle option-icon"></i>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($result['question']['explanation']): ?>
                                            <div class="explanation">
                                                <h5><i class="fas fa-lightbulb"></i> شرح الإجابة:</h5>
                                                <p><?php echo htmlspecialchars($result['question']['explanation']); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                <?php elseif (empty($questions)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <h2>لا توجد أسئلة</h2>
                        <p>لم يتم إضافة أسئلة لهذا الامتحان بعد</p>
                        <a href="lesson_content.php?lesson_id=<?php echo $exam['lesson_id']; ?>" class="btn btn-primary">العودة للدرس</a>
                    </div>

                <?php else: ?>
                    <!-- Exam Instructions -->
                    <div class="exam-instructions">
                        <h3><i class="fas fa-info-circle"></i> تعليمات الامتحان</h3>
                        <ul>
                            <li>مدة الامتحان: <strong><?php echo $exam['duration_minutes']; ?> دقيقة</strong></li>
                            <li>عدد الأسئلة: <strong><?php echo count($questions); ?> سؤال</strong></li>
                            <li>إجمالي النقاط: <strong><?php echo $total_points; ?> نقطة</strong></li>
                            <li>الدرجة المطلوبة للنجاح: <strong><?php echo $passing_score; ?>%</strong></li>
                            <li>يجب الإجابة على جميع الأسئلة</li>
                            <li>تأكد من إجاباتك قبل التسليم</li>
                        </ul>
                    </div>

                    <!-- Timer -->
                    <div class="exam-timer" id="examTimer">
                        <i class="fas fa-clock"></i>
                        <span id="timeRemaining"><?php echo $exam['duration_minutes']; ?>:00</span>
                    </div>

                    <!-- Exam Form -->
                    <form method="POST" class="exam-form" id="examForm">
                        <input type="hidden" name="start_time" value="<?php echo time(); ?>">

                        <div class="questions-container">
                            <?php foreach ($questions as $index => $question): ?>
                                <div class="question-card">
                                    <div class="question-header">
                                        <span class="question-number">السؤال <?php echo $index + 1; ?></span>
                                        <div class="question-meta">
                                            <span class="question-type">
                                                <?php if ($question['question_type'] === 'true_false'): ?>
                                                    <span class="badge badge-info">صح وخطأ</span>
                                                <?php else: ?>
                                                    <span class="badge badge-warning">اختيار متعدد</span>
                                                <?php endif; ?>
                                            </span>
                                            <span class="question-points">
                                                <i class="fas fa-star"></i>
                                                <?php echo $question['points']; ?> نقطة
                                            </span>
                                        </div>
                                    </div>
                                    <div class="question-content">
                                        <h3><?php echo htmlspecialchars($question['question_text']); ?></h3>

                                        <div class="answer-options">
                                            <?php if ($question['question_type'] === 'true_false'): ?>
                                                <label class="option-label">
                                                    <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="true" required>
                                                    <span class="option-text">صح</span>
                                                </label>
                                                <label class="option-label">
                                                    <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="false" required>
                                                    <span class="option-text">خطأ</span>
                                                </label>
                                            <?php else: ?>
                                                <?php
                                                $options = json_decode($question['options'], true);
                                                $option_labels = ['أ', 'ب', 'ج', 'د'];
                                                $option_keys = ['option_a', 'option_b', 'option_c', 'option_d'];
                                                ?>
                                                <?php foreach ($option_keys as $i => $key): ?>
                                                    <?php if (!empty($options[$key])): ?>
                                                        <label class="option-label">
                                                            <input type="radio" name="answers[<?php echo $question['id']; ?>]" value="<?php echo $key; ?>" required>
                                                            <span class="option-letter"><?php echo $option_labels[$i]; ?>)</span>
                                                            <span class="option-text"><?php echo htmlspecialchars($options[$key]); ?></span>
                                                        </label>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="exam-footer">
                            <button type="submit" name="submit_exam" class="btn btn-danger btn-large">
                                <i class="fas fa-paper-plane"></i>
                                إرسال الامتحان
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .exam-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(220, 53, 69, 0.02) 0%,
                rgba(220, 53, 69, 0.05) 50%,
                rgba(220, 53, 69, 0.02) 100%);
            min-height: 100vh;
        }

        .exam-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(220, 53, 69, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .exam-info {
            display: flex;
            align-items: flex-start;
            gap: 25px;
        }

        .exam-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
            flex-shrink: 0;
        }

        .exam-details h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .exam-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .lesson-badge, .subject-badge, .duration-badge, .passing-badge, .completion-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .lesson-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .duration-badge {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            color: white;
        }

        .passing-badge {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .completion-badge.passed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .completion-badge.failed {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .exam-instructions {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }

        .exam-instructions h3 {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .exam-instructions ul {
            margin: 0;
            padding-right: 20px;
            color: #856404;
        }

        .exam-instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .exam-timer {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .questions-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            transition: all 0.3s ease;
        }

        .question-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 15px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .question-meta {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-points {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .question-content {
            padding: 25px;
        }

        .question-content h3 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.5;
        }

        .answer-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .option-label {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option-label:hover {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
        }

        .option-label input[type="radio"] {
            width: 18px;
            height: 18px;
            accent-color: #dc3545;
        }

        .option-letter {
            font-weight: bold;
            color: #dc3545;
            min-width: 20px;
        }

        .option-text {
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.4;
        }

        .exam-footer {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .result-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .result-card.passed {
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .result-card.failed {
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .result-card.passed .result-icon {
            color: #28a745;
        }

        .result-card.failed .result-icon {
            color: #dc3545;
        }

        .result-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .result-content h2 {
            margin: 0 0 25px 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .result-card.passed h2 {
            color: #28a745;
        }

        .result-card.failed h2 {
            color: #dc3545;
        }

        .score-display {
            margin: 25px 0;
        }

        .score-number {
            font-size: 3rem;
            font-weight: bold;
            display: block;
            line-height: 1;
        }

        .result-card.passed .score-number {
            color: #28a745;
        }

        .result-card.failed .score-number {
            color: #dc3545;
        }

        .score-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 8px;
            display: block;
        }

        .score-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            text-align: right;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: #6c757d;
            font-weight: 500;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: 600;
        }

        .result-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-state h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .exam-timer {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                justify-content: center;
            }

            .exam-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .exam-info {
                flex-direction: column;
                text-align: center;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .result-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Detailed Results Styles */
        .detailed-results {
            margin-top: 30px;
        }

        .results-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .results-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .results-container {
            background: white;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .result-question {
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .result-question:last-child {
            border-bottom: none;
        }

        .result-question.correct {
            border-left: 4px solid #28a745;
        }

        .result-question.incorrect {
            border-left: 4px solid #dc3545;
        }

        .result-question-header {
            padding: 20px 25px 15px 25px;
            background: #f8f9fa;
        }

        .question-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .result-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .result-question.correct .result-status {
            color: #28a745;
        }

        .result-question.incorrect .result-status {
            color: #dc3545;
        }

        .earned-points {
            color: #28a745;
        }

        .lost-points {
            color: #dc3545;
        }

        .result-question-content {
            padding: 20px 25px;
        }

        .result-question-content h4 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.5;
        }

        .answer-review {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .answer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .answer-item.user-answer.correct {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .answer-item.user-answer.incorrect {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #721c24;
        }

        .answer-item.correct-answer {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .answer-label {
            font-weight: 600;
            min-width: 100px;
        }

        .answer-value {
            font-weight: 500;
        }

        .options-review {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
        }

        .option-review {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .option-review.correct-option {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #155724;
        }

        .option-review.user-wrong-option {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
            color: #721c24;
        }

        .option-review.user-correct-option {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
            color: #155724;
        }

        .option-review .option-letter {
            font-weight: bold;
            min-width: 20px;
        }

        .option-review.correct-option .option-letter {
            color: #28a745;
        }

        .option-review.user-wrong-option .option-letter {
            color: #dc3545;
        }

        .option-review .option-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }

        .option-icon {
            font-size: 16px;
        }

        .option-review.correct-option .option-icon {
            color: #28a745;
        }

        .option-review.user-wrong-option .option-icon {
            color: #dc3545;
        }

        .explanation {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .explanation h5 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .explanation p {
            color: #856404;
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>

    <script>
        // Timer functionality
        let timeRemaining = <?php echo $exam['duration_minutes'] * 60; ?>; // in seconds
        let timerInterval;

        function startTimer() {
            timerInterval = setInterval(function() {
                timeRemaining--;

                let minutes = Math.floor(timeRemaining / 60);
                let seconds = timeRemaining % 60;

                document.getElementById('timeRemaining').textContent =
                    minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');

                if (timeRemaining <= 0) {
                    clearInterval(timerInterval);
                    alert('انتهى الوقت! سيتم إرسال الامتحان تلقائياً.');
                    document.getElementById('examForm').submit();
                }

                // Warning when 5 minutes left
                if (timeRemaining === 300) {
                    alert('تحذير: باقي 5 دقائق فقط!');
                }

                // Change timer color when time is running out
                let timerElement = document.getElementById('examTimer');
                if (timeRemaining <= 300) { // 5 minutes
                    timerElement.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
                    timerElement.style.animation = 'pulse 1s infinite';
                } else if (timeRemaining <= 600) { // 10 minutes
                    timerElement.style.background = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
                }
            }, 1000);
        }

        // Start timer when page loads (only if exam form exists)
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('examForm')) {
                startTimer();
            }
        });

        // Add pulse animation
        let style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
