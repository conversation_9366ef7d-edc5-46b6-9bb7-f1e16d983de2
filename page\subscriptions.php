<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user's current subscription
    $stmt = $db->prepare("SELECT u.*, sp.name as plan_name, sp.icon as plan_icon, sp.color as plan_color
                         FROM users u
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get all available plans
    $stmt = $db->query("SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY sort_order, price");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $plans = [];
    $user = null;
}

// Check if user has active subscription
$has_subscription = $user && $user['subscription_status'] === 'active' && $user['subscription_end_date'] && strtotime($user['subscription_end_date']) > time();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطط الاشتراك - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Enhanced Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-background-animation"></div>
                <div class="welcome-content">
                    <div class="welcome-text">
                        <div class="welcome-greeting">
                            <h1>
                                خطط الاشتراك
                                <span class="greeting-emoji">💎</span>
                            </h1>
                        </div>
                    </div>
                        
                </div>
            </div>

            <?php if (isset($_GET['error']) && $_GET['error'] === 'active_subscription'): ?>
                <!-- Active Subscription Warning -->
                <div class="dashboard-section">
                    <div class="alert alert-warning" style="
                        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                        border: 1px solid #ffc107;
                        border-radius: 15px;
                        padding: 20px;
                        margin-bottom: 25px;
                        display: flex;
                        align-items: center;
                        gap: 15px;
                    ">
                        <div style="
                            width: 50px;
                            height: 50px;
                            background: #ffc107;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 1.5rem;
                        ">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="margin: 0 0 8px 0; color: #856404;">لديك اشتراك نشط بالفعل!</h4>
                            <p style="margin: 0; color: #856404; line-height: 1.5;">
                                لا يمكنك الاشتراك في خطة جديدة أثناء وجود اشتراك نشط.
                                يمكنك إلغاء الاشتراك الحالي من الأسفل أو انتظار انتهائه.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($has_subscription): ?>
                <!-- Current Subscription Status -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="section-icon">✅</span>
                            اشتراكك الحالي
                        </h2>
                    </div>
                    <div class="subscription-card active">
                        <div class="subscription-header">
                            <div class="plan-icon" style="background-color: <?php echo $user['plan_color']; ?>20; color: <?php echo $user['plan_color']; ?>">
                                <?php echo $user['plan_icon']; ?>
                            </div>
                            <div class="subscription-info">
                                <h3>خطتك النشطة</h3>
                                <h2><?php echo htmlspecialchars($user['plan_name']); ?></h2>
                                <p class="subscription-status active">
                                    <i class="fas fa-check-circle"></i>
                                    نشط حتى <?php echo date('Y-m-d', strtotime($user['subscription_end_date'])); ?>
                                </p>
                            </div>
                        </div>

                        <div class="subscription-actions">
                            <button class="btn btn-secondary" onclick="showCancelModal()">
                                <i class="fas fa-times"></i>
                                إلغاء الاشتراك
                            </button>
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt"></i>
                                الذهاب للداشبورد
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- No Subscription Message -->
                <div class="dashboard-section">
                    <div class="no-subscription-alert">
                        <div class="alert-content">
                            <div class="alert-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="alert-text">
                                <h3>غير مشترك في أي خطة</h3>
                                <p>للوصول إلى المحتوى التعليمي، يمكنك:</p>
                                <ul>
                                    <li><strong>الاشتراك في إحدى الخطط</strong> أدناه</li>
                                    <li><strong>إذا كنت طالب سنتر:</strong> اطلب كود التفعيل من معلمك</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Available Plans -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <span class="section-icon">💎</span>
                        الخطط المتاحة
                    </h2>
                    <p class="section-subtitle">اختر الخطة التي تناسب احتياجاتك التعليمية</p>
                </div>
                <div class="subscription-plans-container">
                    <?php foreach ($plans as $plan): ?>
                        <div class="subscription-plan-card <?php echo $plan['is_popular'] ? 'featured-plan' : ''; ?>" data-plan-id="<?php echo $plan['id']; ?>">
                            <?php if ($plan['is_popular']): ?>
                                <div class="featured-ribbon">
                                    <span>⭐ الأكثر اختياراً</span>
                                </div>
                            <?php endif; ?>

                            <div class="plan-header-section">
                                <div class="plan-icon-wrapper" style="background: linear-gradient(135deg, <?php echo $plan['color']; ?>, <?php echo $plan['color']; ?>80);">
                                    <span class="plan-emoji"><?php echo $plan['icon']; ?></span>
                                </div>
                                <h3 class="plan-title"><?php echo htmlspecialchars($plan['name']); ?></h3>
                                <?php if ($plan['name_en']): ?>
                                    <p class="plan-subtitle"><?php echo htmlspecialchars($plan['name_en']); ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="plan-pricing-section">
                                <?php if ($plan['discount_percentage'] > 0): ?>
                                    <div class="original-price-tag">
                                        <span class="crossed-price"><?php echo number_format($plan['price'], 0); ?> ج.م</span>
                                        <span class="discount-percent">خصم <?php echo $plan['discount_percentage']; ?>%</span>
                                    </div>
                                <?php endif; ?>
                                <div class="current-price-display">
                                    <span class="price-amount"><?php echo number_format($plan['discounted_price'], 0); ?></span>
                                    <span class="price-currency">جنيه مصري</span>
                                </div>
                                <div class="plan-duration"><?php echo $plan['duration_days']; ?> يوم من التعلم المتميز</div>
                            </div>

                            <?php if ($plan['description']): ?>
                                <div class="plan-description-section">
                                    <p><?php echo htmlspecialchars($plan['description']); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if ($plan['features']): ?>
                                <div class="plan-features-section">
                                    <h4>✨ ما ستحصل عليه:</h4>
                                    <ul class="features-list">
                                        <?php
                                        $features = json_decode($plan['features'], true);
                                        foreach ($features as $feature):
                                        ?>
                                            <li class="feature-item">
                                                <span class="feature-check">✓</span>
                                                <span class="feature-text"><?php echo htmlspecialchars($feature); ?></span>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <div class="plan-action-section">
                                <?php if ($has_subscription && $user['current_plan_id'] == $plan['id']): ?>
                                    <button class="plan-button active-plan" disabled>
                                        <span class="button-icon">✅</span>
                                        <span class="button-text">خطتك النشطة</span>
                                    </button>
                                <?php elseif ($has_subscription && $user['current_plan_id'] != $plan['id']): ?>
                                    <button class="plan-button blocked-plan" disabled onclick="showActiveSubscriptionWarning()">
                                        <span class="button-icon">🔒</span>
                                        <span class="button-text">غير متاح</span>
                                        <span class="button-subtitle">لديك اشتراك نشط</span>
                                    </button>
                                <?php else: ?>
                                    <button class="plan-button subscribe-button" onclick="selectPlan(<?php echo $plan['id']; ?>, '<?php echo htmlspecialchars($plan['name']); ?>', <?php echo $plan['discounted_price']; ?>)">
                                        <span class="button-icon">🚀</span>
                                        <span class="button-text">ابدأ الآن</span>
                                        <span class="button-arrow">←</span>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Contact & Support Section -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <span class="section-icon">💬</span>
                        تواصل معنا
                    </h2>
                    <p class="section-subtitle">فريق الدعم متاح لمساعدتك على مدار الساعة</p>
                </div>

                <div class="contact-methods-grid">
                    <div class="contact-method-card whatsapp-card">
                        <div class="contact-method-header">
                            <div class="contact-method-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="contact-method-info">
                                <h4>واتساب</h4>
                                <p>الرد خلال دقائق</p>
                            </div>
                        </div>
                        <a href="https://wa.me/+201234567890" target="_blank" class="contact-method-button">
                            <span>💬 ابدأ المحادثة</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>

                    <div class="contact-method-card telegram-card">
                        <div class="contact-method-header">
                            <div class="contact-method-icon">
                                <i class="fab fa-telegram-plane"></i>
                            </div>
                            <div class="contact-method-info">
                                <h4>تليجرام</h4>
                                <p>انضم للمجتمع</p>
                            </div>
                        </div>
                        <a href="https://t.me/yourchannel" target="_blank" class="contact-method-button">
                            <span>🚀 انضم الآن</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>

                    <div class="contact-method-card facebook-card">
                        <div class="contact-method-header">
                            <div class="contact-method-icon">
                                <i class="fab fa-facebook-f"></i>
                            </div>
                            <div class="contact-method-info">
                                <h4>فيسبوك</h4>
                                <p>تابع آخر الأخبار</p>
                            </div>
                        </div>
                        <a href="https://facebook.com/yourpage" target="_blank" class="contact-method-button">
                            <span>👍 تابعنا</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>

                    <div class="contact-method-card youtube-card">
                        <div class="contact-method-header">
                            <div class="contact-method-icon">
                                <i class="fab fa-youtube"></i>
                            </div>
                            <div class="contact-method-info">
                                <h4>يوتيوب</h4>
                                <p>شروحات مجانية</p>
                            </div>
                        </div>
                        <a href="https://youtube.com/yourchannel" target="_blank" class="contact-method-button">
                            <span>📺 اشترك</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <!-- Cancel Subscription Modal -->
    <div id="cancelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إلغاء الاشتراك</h3>
                <span class="close" onclick="closeModal('cancelModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>هل أنت متأكد من إلغاء الاشتراك؟</h4>
                    <p>سيتم إيقاف الوصول إلى المحتوى التعليمي فور الإلغاء</p>
                </div>
                
                <div class="refund-info">
                    <h4>لاسترداد الأموال:</h4>
                    <p>تواصل معنا على رقم: <strong>01126130559</strong></p>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-danger" onclick="cancelSubscription()">
                    <i class="fas fa-times"></i>
                    تأكيد الإلغاء
                </button>
                <button class="btn btn-secondary" onclick="closeModal('cancelModal')">
                    <i class="fas fa-arrow-right"></i>
                    العودة
                </button>
            </div>
        </div>
    </div>

    <!-- Activation Code Modal -->
    <div id="activationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفعيل بكود السنتر</h3>
                <span class="close" onclick="closeModal('activationModal')">&times;</span>
            </div>
            <form action="activate_code.php" method="POST">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="activation_code">كود التفعيل</label>
                        <input type="text" id="activation_code" name="activation_code" required placeholder="أدخل كود التفعيل">
                    </div>
                    <div class="activation-info">
                        <i class="fas fa-info-circle"></i>
                        <p>احصل على كود التفعيل من معلمك في السنتر</p>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key"></i>
                        تفعيل الكود
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('activationModal')">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function selectPlan(planId, planName, price) {
            // Redirect to payment methods page
            window.location.href = `payment_methods.php?plan_id=${planId}&plan_name=${encodeURIComponent(planName)}&price=${price}`;
        }

        function showActiveSubscriptionWarning() {
            const modal = document.createElement('div');
            modal.className = 'subscription-warning-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    max-width: 500px;
                    width: 90%;
                    padding: 0;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    animation: slideUp 0.3s ease;
                    overflow: hidden;
                ">
                    <div style="
                        background: linear-gradient(135deg, #e53e3e, #c53030);
                        color: white;
                        padding: 25px;
                        text-align: center;
                    ">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 15px;"></i>
                        <h3 style="margin: 0; font-size: 1.5rem;">لديك اشتراك نشط بالفعل!</h3>
                    </div>
                    <div style="padding: 30px; text-align: center;">
                        <p style="margin: 0 0 20px 0; color: #666; line-height: 1.6; font-size: 16px;">
                            لا يمكنك الاشتراك في خطة جديدة أثناء وجود اشتراك نشط.<br>
                            يجب إلغاء الاشتراك الحالي أولاً أو انتظار انتهائه.
                        </p>
                        <div style="
                            background: #f8f9fa;
                            border-radius: 10px;
                            padding: 20px;
                            margin: 20px 0;
                            border-left: 4px solid #4682B4;
                        ">
                            <h4 style="margin: 0 0 10px 0; color: #333;">💡 ماذا يمكنك فعله؟</h4>
                            <ul style="text-align: right; margin: 0; padding-right: 20px; color: #666;">
                                <li>انتظر حتى انتهاء الاشتراك الحالي</li>
                                <li>أو قم بإلغاء الاشتراك من صفحة الإعدادات</li>
                                <li>أو من صفحة المنهج الدراسي</li>
                            </ul>
                        </div>
                    </div>
                    <div style="
                        padding: 20px 30px;
                        border-top: 1px solid #eee;
                        display: flex;
                        gap: 10px;
                        justify-content: center;
                        background: #f8f9fa;
                    ">
                        <button onclick="closeWarningModal()" style="
                            padding: 12px 25px;
                            border: 2px solid #6c757d;
                            background: transparent;
                            color: #6c757d;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#6c757d'; this.style.color='white';" onmouseout="this.style.background='transparent'; this.style.color='#6c757d';">
                            حسناً
                        </button>
                        <a href="settings.php" style="
                            padding: 12px 25px;
                            background: linear-gradient(135deg, #4682B4, #20B2AA);
                            color: white;
                            border-radius: 8px;
                            text-decoration: none;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: inline-flex;
                            align-items: center;
                            gap: 8px;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(70, 130, 180, 0.3)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                            <i class="fas fa-cog"></i>
                            إدارة الاشتراك
                        </a>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function closeWarningModal() {
            const modal = document.querySelector('.subscription-warning-modal');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => modal.remove(), 300);
            }
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            @keyframes slideUp {
                from { transform: translateY(50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        function showCancelModal() {
            document.getElementById('cancelModal').style.display = 'block';
        }

        function showActivationModal() {
            document.getElementById('activationModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function cancelSubscription() {
            if (confirm('هل أنت متأكد من إلغاء الاشتراك؟\nلن تتمكن من الوصول للمحتوى بعد الإلغاء.')) {
                // Send cancellation request
                fetch('cancel_subscription.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'cancel_subscription'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إلغاء الاشتراك بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                });
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>

    <style>
        /* Modern Subscription Page Design */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: backgroundMove 15s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-20px, -20px) scale(1.1); }
        }

        .dashboard-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .dashboard-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        }

        .section-header {
            margin-bottom: 25px;
            text-align: center;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 1.5rem;
        }

        .section-subtitle {
            color: #666;
            font-size: 1rem;
            margin: 0;
        }

        .subscription-card {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(40, 167, 69, 0.2);
            position: relative;
            overflow: hidden;
        }

        .subscription-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .subscription-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
        }

        .subscription-info h3 {
            color: #28a745;
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .subscription-info h2 {
            color: #333;
            margin: 0 0 12px 0;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .subscription-status.active {
            color: #28a745;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .subscription-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        .no-subscription-alert {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
            border-radius: 20px;
            padding: 0;
            border: 2px solid rgba(255, 193, 7, 0.2);
            overflow: hidden;
        }

        .alert-content {
            padding: 30px;
            display: flex;
            align-items: flex-start;
            gap: 25px;
        }

        .alert-icon {
            background: linear-gradient(135deg, #ffc107, #ffb300);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .alert-text h3 {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .alert-text p {
            color: #856404;
            margin: 0 0 15px 0;
            line-height: 1.6;
        }

        .alert-text ul {
            margin: 0 0 0 20px;
            color: #856404;
            line-height: 1.8;
        }

        .alert-text li {
            margin-bottom: 8px;
        }

        /* Subscription Plans Container */
        .subscription-plans-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin: 40px 0;
        }

        .subscription-plan-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            padding: 0;
            box-shadow:
                0 16px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .subscription-plan-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 24px 48px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.4);
        }

        .subscription-plan-card.featured-plan {
            border: 2px solid #ffd700;
            transform: scale(1.05);
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 255, 255, 0.98));
        }

        .subscription-plan-card.featured-plan:hover {
            transform: translateY(-8px) scale(1.08);
        }

        .featured-ribbon {
            position: absolute;
            top: 20px;
            right: -30px;
            background: linear-gradient(135deg, #ffd700, #ffb347);
            color: #333;
            padding: 8px 40px;
            font-size: 12px;
            font-weight: 700;
            transform: rotate(45deg);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
            z-index: 10;
        }

        /* Plan Card Sections */
        .plan-header-section {
            padding: 32px 32px 24px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .plan-icon-wrapper {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .plan-emoji {
            font-size: 2.5rem;
        }

        .plan-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 8px 0;
        }

        .plan-subtitle {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }

        .plan-pricing-section {
            padding: 24px 32px;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .original-price-tag {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .crossed-price {
            text-decoration: line-through;
            color: #a0aec0;
            font-size: 16px;
        }

        .discount-percent {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .current-price-display {
            margin-bottom: 12px;
        }

        .price-amount {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .price-currency {
            font-size: 16px;
            color: #4a5568;
            font-weight: 600;
        }

        .plan-duration {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .plan-description-section {
            padding: 20px 32px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .plan-description-section p {
            color: #4a5568;
            line-height: 1.6;
            margin: 0;
            text-align: center;
        }

        .plan-features-section {
            padding: 24px 32px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .plan-features-section h4 {
            color: #2d3748;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 16px 0;
            text-align: center;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            transition: all 0.2s ease;
        }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            padding: 8px 12px;
        }

        .feature-check {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .feature-text {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }

        .plan-action-section {
            padding: 32px;
        }

        .plan-button {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .subscribe-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .subscribe-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
        }

        .subscribe-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .subscribe-button:hover::before {
            left: 100%;
        }

        .active-plan {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            cursor: default;
        }

        .blocked-plan {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            cursor: not-allowed;
            opacity: 0.8;
            position: relative;
        }

        .blocked-plan .button-subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 2px;
            display: block;
        }

        .blocked-plan:hover {
            transform: none;
            box-shadow: none;
            opacity: 0.9;
        }

        .button-icon {
            font-size: 18px;
        }

        .button-text {
            font-weight: 600;
        }

        .button-arrow {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .subscribe-button:hover .button-arrow {
            transform: translateX(-4px);
        }

        .plan .pricing {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #bed6fb;
            border-radius: 99em 0 0 99em;
            display: flex;
            align-items: center;
            flex-direction: column;
            padding: 0.625em 0.75em;
            font-size: 1.25rem;
            font-weight: 600;
            color: #425475;
            min-width: 120px;
        }

        .plan .pricing small {
            color: #707a91;
            font-size: 0.75em;
            margin-top: 0.25em;
        }

        .plan .discount-badge {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 700;
            margin-top: 5px;
        }

        .plan .title {
            font-weight: 600;
            font-size: 1.25rem;
            color: #425675;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
            margin-bottom: 15px;
        }

        .plan .plan-icon {
            font-size: 1.5rem;
        }

        .plan .info {
            text-align: center;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .plan .features {
            display: flex;
            flex-direction: column;
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }

        .plan .features li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .plan .features .icon {
            background-color: #1FCAC5;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .plan .features .icon svg {
            width: 14px;
            height: 14px;
        }

        .plan .action {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .plan .button {
            background-color: #6558d3;
            border-radius: 6px;
            color: #fff;
            font-weight: 500;
            font-size: 1.125rem;
            text-align: center;
            border: 0;
            outline: 0;
            width: 100%;
            padding: 0.625em 0.75em;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .plan .button:hover, .plan .button:focus {
            background-color: #4133B7;
            transform: translateY(-2px);
        }

        .plan .button.current-plan {
            background-color: #28a745;
            cursor: default;
        }

        .plan .button.current-plan:hover {
            background-color: #28a745;
            transform: none;
        }

        /* Contact Methods Grid */
        .contact-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }

        .contact-method-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .contact-method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }

        .whatsapp-card::before {
            background: linear-gradient(90deg, #25d366, #128c7e);
        }

        .telegram-card::before {
            background: linear-gradient(90deg, #0088cc, #229ed9);
        }

        .facebook-card::before {
            background: linear-gradient(90deg, #1877f2, #42a5f5);
        }

        .youtube-card::before {
            background: linear-gradient(90deg, #ff0000, #cc0000);
        }

        .contact-method-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
        }

        .contact-method-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .contact-method-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .whatsapp-card .contact-method-icon {
            background: linear-gradient(135deg, #25d366, #128c7e);
        }

        .telegram-card .contact-method-icon {
            background: linear-gradient(135deg, #0088cc, #229ed9);
        }

        .facebook-card .contact-method-icon {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }

        .youtube-card .contact-method-icon {
            background: linear-gradient(135deg, #ff0000, #cc0000);
        }

        .contact-method-info h4 {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 4px 0;
        }

        .contact-method-info p {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }

        .contact-method-button {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            text-decoration: none;
            color: #4a5568;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .contact-method-button:hover {
            background: linear-gradient(135deg, #edf2f7, #e2e8f0);
            transform: translateY(-1px);
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .contact-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.8rem;
            color: white;
        }

        .contact-icon.whatsapp {
            background: linear-gradient(135deg, #25D366, #128C7E);
        }

        .contact-icon.facebook {
            background: linear-gradient(135deg, #1877F2, #42A5F5);
        }

        .contact-icon.telegram {
            background: linear-gradient(135deg, #0088CC, #229ED9);
        }

        .contact-icon.youtube {
            background: linear-gradient(135deg, #FF0000, #CC0000);
        }

        .contact-card h3 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .contact-card p {
            color: #666;
            margin: 0 0 20px 0;
            font-size: 14px;
        }

        .contact-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            color: white;
        }

        .whatsapp-btn {
            background: linear-gradient(135deg, #25D366, #128C7E);
        }

        .whatsapp-btn:hover {
            background: linear-gradient(135deg, #128C7E, #25D366);
            transform: translateY(-2px);
        }

        .facebook-btn {
            background: linear-gradient(135deg, #1877F2, #42A5F5);
        }

        .facebook-btn:hover {
            background: linear-gradient(135deg, #42A5F5, #1877F2);
            transform: translateY(-2px);
        }

        .telegram-btn {
            background: linear-gradient(135deg, #0088CC, #229ED9);
        }

        .telegram-btn:hover {
            background: linear-gradient(135deg, #229ED9, #0088CC);
            transform: translateY(-2px);
        }

        .youtube-btn {
            background: linear-gradient(135deg, #FF0000, #CC0000);
        }

        .youtube-btn:hover {
            background: linear-gradient(135deg, #CC0000, #FF0000);
            transform: translateY(-2px);
        }

        .plan-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.4s ease;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .plan-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .plan-card.popular {
            border: 2px solid #ffc107;
            transform: scale(1.05);
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.1) 100%);
        }

        .plan-card.popular:hover {
            transform: translateY(-8px) scale(1.08);
        }

        .popular-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .plan-header {
            padding: 30px 30px 20px 30px;
            text-align: center;
        }

        .plan-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 20px auto;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .plan-header h3 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .plan-subtitle {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .plan-pricing {
            padding: 0 30px 20px 30px;
            text-align: center;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .discount-badge {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            margin: 8px 0;
            display: inline-block;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .current-price {
            font-size: 2.2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 10px 0;
        }

        .duration {
            color: #666;
            font-size: 15px;
            font-weight: 500;
        }

        .plan-description {
            padding: 0 30px 20px 30px;
        }

        .plan-description p {
            color: #666;
            margin: 0;
            line-height: 1.7;
            font-size: 15px;
        }

        .plan-features {
            padding: 0 30px 20px 30px;
        }

        .plan-features h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 17px;
            font-weight: 600;
        }

        .plan-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .plan-features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .plan-features li i {
            color: #28a745;
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .plan-actions {
            padding: 25px 30px 30px 30px;
            text-align: center;
        }

        .plan-select-btn {
            width: 100%;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .plan-select-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .center-card {
            background: linear-gradient(135deg, rgba(21, 101, 192, 0.05) 0%, rgba(21, 101, 192, 0.1) 100%);
            border-radius: 20px;
            padding: 35px;
            display: flex;
            align-items: center;
            gap: 30px;
            border: 2px solid rgba(21, 101, 192, 0.1);
            transition: all 0.3s ease;
        }

        .center-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(21, 101, 192, 0.15);
        }

        .center-icon {
            background: linear-gradient(135deg, #1565c0, #1976d2);
            color: white;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            flex-shrink: 0;
            box-shadow: 0 8px 20px rgba(21, 101, 192, 0.3);
        }

        .center-content h3 {
            color: #1565c0;
            margin: 0 0 15px 0;
            font-size: 1.6rem;
            font-weight: 700;
        }

        .center-content p {
            color: #1976d2;
            margin: 0 0 20px 0;
            line-height: 1.7;
            font-size: 15px;
        }

        .center-activation-btn {
            padding: 12px 25px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .center-activation-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(21, 101, 192, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-actions {
            padding: 20px;
            border-top: 1px solid #ddd;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            background: #f8f9fa;
        }

        .warning-message {
            text-align: center;
            margin-bottom: 20px;
        }

        .warning-message i {
            font-size: 3rem;
            color: #dc3545;
            margin-bottom: 15px;
        }

        .warning-message h4 {
            color: #dc3545;
            margin: 0 0 10px 0;
        }

        .refund-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }

        .refund-info h4 {
            color: #17a2b8;
            margin: 0 0 10px 0;
        }

        .activation-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }

        .activation-info i {
            color: #1565c0;
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #20B2AA 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #1565c0;
            color: #1565c0;
            box-shadow: 0 4px 15px rgba(21, 101, 192, 0.2);
        }

        .btn-outline:hover {
            background: linear-gradient(135deg, #1565c0, #1976d2);
            color: white;
            border-color: #1976d2;
        }

        @media (max-width: 768px) {
            .plans-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .plan-card {
                margin: 0 10px;
            }

            .center-card {
                flex-direction: column;
                text-align: center;
                padding: 25px;
                gap: 20px;
            }

            .subscription-header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .subscription-actions {
                justify-content: center;
                flex-wrap: wrap;
                gap: 10px;
            }

            .alert-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
                padding: 25px;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .welcome-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .welcome-stats {
                justify-content: center;
            }

            .dashboard-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .plan-header {
                padding: 25px 20px 15px 20px;
            }

            .plan-pricing, .plan-description, .plan-features, .plan-actions {
                padding-left: 20px;
                padding-right: 20px;
            }
        }

        @media (max-width: 480px) {
            .dashboard-section {
                padding: 15px;
            }

            .plan-card {
                margin: 0 5px;
            }

            .welcome-greeting h1 {
                font-size: 1.8rem;
            }

            .current-price {
                font-size: 1.8rem;
            }

            .plan-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }
    </style>

    <!-- Include Dashboard Scripts -->
    <script src="<?php echo SITE_URL; ?>/js/dashboard.js"></script>

    <?php
    // Show subscription expiry notification if needed
    include __DIR__ . '/../includes/subscription_notification.php';
    ?>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
