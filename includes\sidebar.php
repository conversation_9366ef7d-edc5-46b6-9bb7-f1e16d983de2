<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get user information
require_once 'database.php';
$userManager = new UserManager();

// Get full user data including education info
$userData = $userManager->getUserById($_SESSION['user_id']);
$firstName = $userData['first_name'] ?? $_SESSION['first_name'] ?? 'المستخدم';
$username = $userData['username'] ?? $_SESSION['username'] ?? '';

// Format Arabic grade display
function formatArabicGrade($educationLevel, $educationType, $grade) {
    $levels = [
        'primary' => 'ابتدائي',
        'preparatory' => 'إعدادي', 
        'secondary' => 'ثانوي'
    ];
    
    $types = [
        'azhari' => 'أزهري',
        'general' => 'عام'
    ];
    
    $gradeNumbers = [
        '1' => 'الأول',
        '2' => 'الثاني', 
        '3' => 'الثالث',
        '4' => 'الرابع',
        '5' => 'الخامس',
        '6' => 'السادس'
    ];
    
    $levelText = $levels[$educationLevel] ?? $educationLevel;
    $typeText = $types[$educationType] ?? $educationType;
    $gradeText = $gradeNumbers[$grade] ?? $grade;
    
    return "الصف {$gradeText} {$levelText} {$typeText}";
}

$gradeDisplay = '';
if ($userData) {
    $gradeDisplay = formatArabicGrade(
        $userData['education_level'], 
        $userData['education_type'], 
        $userData['grade']
    );
}

// Generate user initials
$initials = mb_substr($firstName, 0, 1, 'UTF-8');
if (isset($userData['second_name'])) {
    $initials .= mb_substr($userData['second_name'], 0, 1, 'UTF-8');
}
?>

<aside class="dashboard-sidebar" id="dashboardSidebar">
    <div class="sidebar-overlay" onclick="toggleSidebar()"></div>
    <div class="sidebar-content">
        <!-- Sidebar Header with Close Button -->
        <div class="sidebar-header">
            <button class="sidebar-close" onclick="toggleSidebar()" aria-label="إغلاق القائمة">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>

        <!-- User Profile Section -->
        <div class="sidebar-user">
            <div class="user-avatar-large">
                <?php echo htmlspecialchars($initials); ?>
            </div>
            <div class="user-info">
                <h3 class="user-name"><?php echo htmlspecialchars($firstName); ?></h3>
                <p class="user-grade"><?php echo htmlspecialchars($gradeDisplay); ?></p>
                <p class="user-username">@<?php echo htmlspecialchars($username); ?></p>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/dashboard.php" class="nav-link active" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/curriculum.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">المنهج</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/courses.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">🎓</span>
                        <span class="nav-text">الكورسات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/ask_teacher.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">❓</span>
                        <span class="nav-text">اسأل معلم</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/my_messages.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">📧</span>
                        <span class="nav-text">رسائلي</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/subscriptions.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">💳</span>
                        <span class="nav-text">الاشتراكات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/settings.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">الإعدادات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>/page/profile.php" class="nav-link" onclick="closeSidebarOnMobile()">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">تعديل الملف الشخصي</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Logout Button -->
        <div class="sidebar-footer">
            <a href="<?php echo SITE_URL; ?>/logout.php" class="logout-btn">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">تسجيل الخروج</span>
            </a>
        </div>
    </div>
</aside>

<!-- Mobile Bottom Navigation -->
<nav class="mobile-bottom-nav" id="mobileBottomNav">
    <a href="<?php echo SITE_URL; ?>/page/dashboard.php" class="mobile-nav-item active">
        <span class="mobile-nav-icon">🏠</span>
        <span class="mobile-nav-text">الرئيسية</span>
    </a>
    <a href="<?php echo SITE_URL; ?>/page/curriculum.php" class="mobile-nav-item">
        <span class="mobile-nav-icon">📚</span>
        <span class="mobile-nav-text">المنهج</span>
    </a>
    <a href="<?php echo SITE_URL; ?>/page/courses.php" class="mobile-nav-item">
        <span class="mobile-nav-icon">🎓</span>
        <span class="mobile-nav-text">الكورسات</span>
    </a>
    <a href="<?php echo SITE_URL; ?>/page/ask_teacher.php" class="mobile-nav-item">
        <span class="mobile-nav-icon">❓</span>
        <span class="mobile-nav-text">اسأل</span>
    </a>
    <a href="<?php echo SITE_URL; ?>/page/subscriptions.php" class="mobile-nav-item">
        <span class="mobile-nav-icon">💳</span>
        <span class="mobile-nav-text">الاشتراكات</span>
    </a>
    <a href="<?php echo SITE_URL; ?>/page/profile.php" class="mobile-nav-item">
        <span class="mobile-nav-icon">⚙️</span>
        <span class="mobile-nav-text">الإعدادات</span>
    </a>
</nav>

<style>
.dashboard-sidebar {
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-left: 2px solid rgba(70, 130, 180, 0.1);
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -4px 0 20px rgba(70, 130, 180, 0.1);
}

.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.sidebar-header {
    display: none;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(70, 130, 180, 0.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.sidebar-close {
    background: none;
    border: none;
    color: #4682B4;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.sidebar-close:hover {
    background: rgba(70, 130, 180, 0.1);
    transform: rotate(90deg);
}

.sidebar-content {
    padding: 20px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.sidebar-user {
    padding: 0 20px 25px;
    text-align: center;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    margin: 0 15px 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
}

.user-avatar-large {
    width: 85px;
    height: 85px;
    border-radius: 50%;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 26px;
    margin: 20px auto 15px;
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
    border: 3px solid white;
    transition: all 0.3s ease;
}

.user-avatar-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(70, 130, 180, 0.4);
}

.user-info {
    text-align: center;
    padding-bottom: 20px;
}

.user-name {
    font-size: 19px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px;
}

.user-grade {
    font-size: 14px;
    color: #4682B4;
    margin: 0 0 8px;
    font-weight: 600;
    background: rgba(70, 130, 180, 0.1);
    padding: 4px 12px;
    border-radius: 20px;
    display: inline-block;
}

.user-username {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

.sidebar-nav {
    flex: 1;
    padding: 0 15px;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 8px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 14px 18px;
    color: #4a5568;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    box-sizing: border-box;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    color: #4682B4;
    transform: translateX(-3px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.2);
}

.nav-link.active {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
    transform: translateX(-3px);
}

.nav-link.active::before {
    display: none;
}

.nav-icon {
    font-size: 20px;
    margin-left: 15px;
    width: 24px;
    text-align: center;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon,
.nav-link.active .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-size: 15px;
    font-weight: 600;
}

.sidebar-footer {
    padding: 20px 15px;
    border-top: 2px solid rgba(70, 130, 180, 0.1);
    margin-top: auto;
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 14px 18px;
    color: #dc3545;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 600;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border: 2px solid rgba(220, 53, 69, 0.2);
    min-height: 48px;
    box-sizing: border-box;
}

.logout-btn:hover {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    transform: translateX(-3px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

.logout-btn .nav-icon {
    font-size: 20px;
    margin-left: 15px;
    width: 24px;
    text-align: center;
}

.logout-btn .nav-text {
    font-size: 15px;
    font-weight: 600;
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-top: 2px solid rgba(70, 130, 180, 0.1);
    padding: 8px 0 12px;
    z-index: 1000;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -4px 20px rgba(70, 130, 180, 0.15);
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #6c757d;
    padding: 8px 6px;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-width: 50px;
    min-height: 48px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.mobile-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.3), transparent);
    transition: left 0.5s ease;
}

.mobile-nav-item:hover::before {
    left: 100%;
}

.mobile-nav-item.active {
    color: #4682B4;
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.2);
}

.mobile-nav-item:hover {
    color: #4682B4;
    background: rgba(70, 130, 180, 0.1);
    transform: translateY(-1px);
}

.mobile-nav-icon {
    font-size: 18px;
    margin-bottom: 2px;
    transition: transform 0.3s ease;
}

.mobile-nav-item.active .mobile-nav-icon,
.mobile-nav-item:hover .mobile-nav-icon {
    transform: scale(1.1);
}

.mobile-nav-text {
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-sidebar {
        transform: translateX(100%);
        width: 100%;
        max-width: 320px;
        z-index: 1001;
    }

    .dashboard-sidebar.show {
        transform: translateX(0);
    }

    .dashboard-sidebar.show .sidebar-overlay {
        display: block;
    }

    .sidebar-header {
        display: block;
    }

    .sidebar-content {
        padding: 10px 0;
    }

    .sidebar-user {
        margin: 0 10px 20px;
        padding: 15px;
    }

    .user-avatar-large {
        width: 70px;
        height: 70px;
        font-size: 22px;
        margin: 15px auto 12px;
    }

    .user-name {
        font-size: 17px;
    }

    .user-grade {
        font-size: 13px;
        padding: 3px 10px;
    }

    .user-username {
        font-size: 12px;
    }

    .sidebar-nav {
        padding: 0 10px;
    }

    .nav-link {
        padding: 12px 15px;
        min-height: 44px;
    }

    .nav-icon {
        font-size: 18px;
        margin-left: 12px;
        width: 20px;
    }

    .nav-text {
        font-size: 14px;
    }

    .sidebar-footer {
        padding: 15px 10px;
    }

    .logout-btn {
        padding: 12px 15px;
        min-height: 44px;
    }

    .mobile-bottom-nav {
        display: flex;
    }

    /* Add padding to body to account for bottom nav */
    body {
        padding-bottom: 75px;
    }
}

@media (max-width: 480px) {
    .dashboard-sidebar {
        max-width: 280px;
    }

    .sidebar-user {
        margin: 0 8px 15px;
        padding: 12px;
    }

    .user-avatar-large {
        width: 60px;
        height: 60px;
        font-size: 18px;
        margin: 12px auto 10px;
    }

    .user-name {
        font-size: 15px;
    }

    .user-grade {
        font-size: 12px;
        padding: 2px 8px;
    }

    .user-username {
        font-size: 11px;
    }

    .sidebar-nav {
        padding: 0 8px;
    }

    .nav-link {
        padding: 10px 12px;
        min-height: 40px;
    }

    .nav-icon {
        font-size: 16px;
        margin-left: 10px;
        width: 18px;
    }

    .nav-text {
        font-size: 13px;
    }

    .sidebar-footer {
        padding: 12px 8px;
    }

    .logout-btn {
        padding: 10px 12px;
        min-height: 40px;
    }

    .mobile-bottom-nav {
        padding: 6px 0 10px;
    }

    .mobile-nav-item {
        min-width: 45px;
        min-height: 44px;
        padding: 6px 4px;
    }

    .mobile-nav-text {
        font-size: 9px;
    }

    .mobile-nav-icon {
        font-size: 16px;
    }

    body {
        padding-bottom: 70px;
    }
}

@media (max-width: 320px) {
    .dashboard-sidebar {
        max-width: 260px;
    }

    .sidebar-user {
        margin: 0 6px 12px;
        padding: 10px;
    }

    .user-avatar-large {
        width: 50px;
        height: 50px;
        font-size: 16px;
        margin: 10px auto 8px;
    }

    .user-name {
        font-size: 14px;
    }

    .user-grade {
        font-size: 11px;
        padding: 2px 6px;
    }

    .user-username {
        font-size: 10px;
    }

    .nav-link {
        padding: 8px 10px;
        min-height: 36px;
    }

    .nav-icon {
        font-size: 14px;
        margin-left: 8px;
        width: 16px;
    }

    .nav-text {
        font-size: 12px;
    }

    .logout-btn {
        padding: 8px 10px;
        min-height: 36px;
    }

    .mobile-nav-item {
        min-width: 40px;
        min-height: 40px;
        padding: 4px 2px;
    }

    .mobile-nav-text {
        font-size: 8px;
    }

    .mobile-nav-icon {
        font-size: 14px;
    }

    body {
        padding-bottom: 65px;
    }
}

/* Message notification badges */
.nav-badge.unread-replies {
    background: #27ae60;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>

<script>
// Set active navigation item based on current page
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop();
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-item');

    navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'dashboard.php')) {
            link.classList.add('active');
        }
    });

    // Update hamburger menu toggle button state
    updateHamburgerState();
});

// Toggle sidebar on mobile
function toggleSidebar() {
    const sidebar = document.getElementById('dashboardSidebar');
    const hamburger = document.querySelector('.mobile-menu-toggle');

    sidebar.classList.toggle('show');

    if (hamburger) {
        hamburger.classList.toggle('active');
    }

    // Prevent body scroll when sidebar is open on mobile
    if (window.innerWidth <= 768) {
        document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
    }
}

// Close sidebar on mobile when navigation link is clicked
function closeSidebarOnMobile() {
    if (window.innerWidth <= 768) {
        const sidebar = document.getElementById('dashboardSidebar');
        const hamburger = document.querySelector('.mobile-menu-toggle');

        sidebar.classList.remove('show');
        if (hamburger) {
            hamburger.classList.remove('active');
        }
        document.body.style.overflow = '';
    }
}

// Update hamburger menu state
function updateHamburgerState() {
    const sidebar = document.getElementById('dashboardSidebar');
    const hamburger = document.querySelector('.mobile-menu-toggle');

    if (hamburger && sidebar) {
        if (sidebar.classList.contains('show')) {
            hamburger.classList.add('active');
        } else {
            hamburger.classList.remove('active');
        }
    }
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('dashboardSidebar');
    const isClickInsideSidebar = sidebar.contains(event.target);
    const isClickOnToggleButton = event.target.closest('.mobile-menu-toggle') || event.target.closest('.sidebar-toggle');
    const isClickOnOverlay = event.target.classList.contains('sidebar-overlay');

    if ((!isClickInsideSidebar && !isClickOnToggleButton) || isClickOnOverlay) {
        if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
            toggleSidebar();
        }
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('dashboardSidebar');
    const hamburger = document.querySelector('.mobile-menu-toggle');

    if (window.innerWidth > 768) {
        // Desktop view
        sidebar.classList.remove('show');
        if (hamburger) {
            hamburger.classList.remove('active');
        }
        document.body.style.overflow = '';
    }
});

// Handle escape key to close sidebar
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const sidebar = document.getElementById('dashboardSidebar');
        if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
            toggleSidebar();
        }
    }
});

// Add touch support for mobile swipe gestures
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', function(event) {
    touchStartX = event.changedTouches[0].screenX;
});

document.addEventListener('touchend', function(event) {
    touchEndX = event.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const sidebar = document.getElementById('dashboardSidebar');
    const swipeThreshold = 50;

    if (window.innerWidth <= 768) {
        // Swipe right to left (close sidebar)
        if (touchStartX - touchEndX > swipeThreshold && sidebar.classList.contains('show')) {
            toggleSidebar();
        }
        // Swipe left to right (open sidebar) - only from edge
        else if (touchEndX - touchStartX > swipeThreshold && touchStartX < 50 && !sidebar.classList.contains('show')) {
            toggleSidebar();
        }
    }
}
</script>
