<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access or pending
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus) {
    if ($userCourseStatus['activation_status'] === 'active') {
        header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
        exit;
    } elseif ($userCourseStatus['activation_status'] === 'pending') {
        header('Location: ' . SITE_URL . '/page/course_details.php?id=' . $courseId . '&message=pending');
        exit;
    }
}

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['activation_method'])) {
        $activationMethod = $_POST['activation_method'];

        // Don't create subscription here - only redirect to activation page
        // Subscription will be created when user actually completes the activation process

        if ($activationMethod === 'code') {
            header('Location: ' . SITE_URL . '/page/course_activate_code.php?id=' . $courseId);
            exit;
        } elseif ($activationMethod === 'fawry') {
            header('Location: ' . SITE_URL . '/page/course_activate_fawry.php?id=' . $courseId);
            exit;
        } elseif ($activationMethod === 'paymob') {
            header('Location: ' . SITE_URL . '/page/course_activate_paymob.php?id=' . $courseId);
            exit;
        } elseif ($activationMethod === 'wallet') {
            header('Location: ' . SITE_URL . '/page/course_activate_wallet.php?id=' . $courseId);
            exit;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل في الكورس - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="<?php echo SITE_URL; ?>/page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="<?php echo SITE_URL; ?>/page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="<?php echo SITE_URL; ?>/page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>التسجيل</span>
                </div>

                <div class="registration-container">
                    <!-- Course Summary -->
                    <div class="course-summary-card">
                        <div class="course-summary-image">
                            <?php if ($course['main_image']): ?>
                                <img src="<?php echo SITE_URL; ?>/uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-summary-info">
                            <h2><?php echo htmlspecialchars($course['title']); ?></h2>
                            <p class="course-subject"><?php echo htmlspecialchars($course['subject']); ?></p>
                            
                            <div class="course-price">
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <span class="original-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <span class="discounted-price"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                    <span class="discount-badge">خصم <?php echo $course['discount_percentage']; ?>%</span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Form -->
                    <div class="registration-form-card">
                        <div class="form-header">
                            <h1>🔓 تفعيل الكورس</h1>
                            <p>اختر طريقة التفعيل المناسبة لك</p>
                        </div>

                        <!-- Important Notice -->
                        <div class="activation-notice">
                            <div class="notice-icon">📢</div>
                            <div class="notice-content">
                                <h3>معلومات مهمة للتفعيل:</h3>
                                <p>
                                    يمكنك طلب التفعيل عن طريق تحويل مبلغ الكورس إلى <strong>01126130559</strong>، 
                                    ثم اذهب إلى خيار طلب التفعيل وأرسل الإيصال مع رقم التحويل وطريقة التحويل 
                                    (فودافون كاش، اتصالات كاش، وي كاش، أو أورانج كاش). 
                                    أو تواصل مع <strong>01128031228</strong> عبر الواتساب لطلب كود التفعيل.
                                </p>
                            </div>
                        </div>

                        <!-- Activation Methods -->
                        <form method="POST" class="activation-form">
                            <div class="activation-methods">
                                <div class="method-option" onclick="selectMethod('code')">
                                    <input type="radio" name="activation_method" value="code" id="method_code">
                                    <label for="method_code" class="method-card">
                                        <div class="method-icon">🔑</div>
                                        <div class="method-info">
                                            <h3>تفعيل بالكود</h3>
                                            <p>استخدم كود التفعيل المرسل إليك</p>
                                            <ul class="method-features">
                                                <li>تفعيل فوري</li>
                                                <li>لا يحتاج مراجعة</li>
                                                <li>سهل وسريع</li>
                                            </ul>
                                        </div>
                                        <div class="method-badge">موصى به</div>
                                    </label>
                                </div>

                                <div class="method-option" onclick="selectMethod('fawry')">
                                    <input type="radio" name="activation_method" value="fawry" id="method_fawry">
                                    <label for="method_fawry" class="method-card">
                                        <div class="method-icon">🏪</div>
                                        <div class="method-info">
                                            <h3>الدفع عبر فوري</h3>
                                            <p>ادفع من أي فرع فوري أو عبر التطبيق</p>
                                            <ul class="method-features">
                                                <li>تفعيل فوري</li>
                                                <li>دفع آمن ومضمون</li>
                                                <li>متاح في جميع أنحاء مصر</li>
                                            </ul>
                                        </div>
                                    </label>
                                </div>

                                <div class="method-option" onclick="selectMethod('paymob')">
                                    <input type="radio" name="activation_method" value="paymob" id="method_paymob">
                                    <label for="method_paymob" class="method-card">
                                        <div class="method-icon">💳</div>
                                        <div class="method-info">
                                            <h3>الدفع بالفيزا</h3>
                                            <p>ادفع بالفيزا أو الماستركارد</p>
                                            <ul class="method-features">
                                                <li>تفعيل فوري</li>
                                                <li>دفع آمن ومشفر</li>
                                                <li>يقبل جميع البطاقات</li>
                                            </ul>
                                        </div>
                                    </label>
                                </div>

                                <div class="method-option" onclick="selectMethod('wallet')">
                                    <input type="radio" name="activation_method" value="wallet" id="method_wallet">
                                    <label for="method_wallet" class="method-card">
                                        <div class="method-icon">📱</div>
                                        <div class="method-info">
                                            <h3>المحافظ الإلكترونية</h3>
                                            <p>ادفع عبر فودافون كاش، اتصالات كاش، وي كاش</p>
                                            <ul class="method-features">
                                                <li>تفعيل فوري</li>
                                                <li>دفع سهل وسريع</li>
                                                <li>جميع المحافظ الإلكترونية</li>
                                            </ul>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large" id="submitBtn" disabled>
                                    <span class="btn-icon">🚀</span>
                                    متابعة التفعيل
                                </button>
                                <a href="<?php echo SITE_URL; ?>/page/course_details.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    العودة لتفاصيل الكورس
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .registration-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            align-items: start;
        }

        .course-summary-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
        }

        .course-summary-image {
            width: 100%;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .course-summary-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 60px;
            color: white;
        }

        .course-summary-info h2 {
            color: #2c3e50;
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .course-subject {
            color: #4682B4;
            font-size: 16px;
            font-weight: 600;
            background: rgba(135, 206, 235, 0.1);
            padding: 6px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 20px;
        }

        .course-price {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 16px;
        }

        .discounted-price {
            color: #dc3545;
            font-size: 24px;
            font-weight: 700;
        }

        .current-price {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
        }

        .discount-badge {
            background: #dc3545;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            align-self: flex-start;
        }

        .registration-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #6c757d;
            font-size: 18px;
        }

        .activation-notice {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            display: flex;
            gap: 20px;
        }

        .notice-icon {
            font-size: 40px;
            flex-shrink: 0;
        }

        .notice-content h3 {
            color: #856404;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .notice-content p {
            color: #856404;
            line-height: 1.6;
            margin: 0;
        }

        .activation-methods {
            display: grid;
            gap: 20px;
            margin-bottom: 40px;
        }

        .method-option {
            cursor: pointer;
        }

        .method-option input[type="radio"] {
            display: none;
        }

        .method-card {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px;
            border: 3px solid #dee2e6;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background: #f8f9fa;
        }

        .method-option input[type="radio"]:checked + .method-card {
            border-color: #4682B4;
            background: rgba(135, 206, 235, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.2);
        }

        .method-card:hover {
            border-color: #87CEEB;
            transform: translateY(-2px);
        }

        .method-icon {
            font-size: 48px;
            flex-shrink: 0;
        }

        .method-info {
            flex: 1;
        }

        .method-info h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .method-info p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .method-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .method-features li {
            color: #495057;
            font-size: 14px;
            margin-bottom: 5px;
            padding-right: 20px;
            position: relative;
        }

        .method-features li:before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            position: absolute;
            right: 0;
        }

        .method-badge {
            position: absolute;
            top: -10px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-large:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .registration-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .course-summary-card {
                position: static;
            }

            .registration-form-card {
                padding: 25px;
            }

            .form-header h1 {
                font-size: 24px;
            }

            .activation-notice {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .method-card {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function selectMethod(method) {
            const radio = document.getElementById('method_' + method);
            radio.checked = true;
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = false;
        }

        // Enable submit button when any method is selected
        document.addEventListener('DOMContentLoaded', function() {
            const radios = document.querySelectorAll('input[name="activation_method"]');
            const submitBtn = document.getElementById('submitBtn');
            
            radios.forEach(radio => {
                radio.addEventListener('change', function() {
                    submitBtn.disabled = false;
                });
            });
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
