<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/MessageManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$messageManager = new MessageManager();
$userManager = new UserManager();

// Get user info
$user = $userManager->getUserById($_SESSION['user_id']);

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    try {
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $messageType = $_POST['message_type'] ?? 'question';
        $priority = $_POST['priority'] ?? 'medium';
        $categoryId = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;

        // Validation
        if (empty($subject)) {
            throw new Exception('الموضوع مطلوب');
        }
        if (empty($message)) {
            throw new Exception('الرسالة مطلوبة');
        }
        if (strlen($subject) > 255) {
            throw new Exception('الموضوع طويل جداً (الحد الأقصى 255 حرف)');
        }
        if (strlen($message) > 5000) {
            throw new Exception('الرسالة طويلة جداً (الحد الأقصى 5000 حرف)');
        }

        // Send message
        $messageId = $messageManager->sendMessage(
            $_SESSION['user_id'],
            $subject,
            $message,
            $messageType,
            $categoryId,
            $priority
        );

        if ($messageId) {
            $success_message = 'تم إرسال رسالتك بنجاح! سيتم الرد عليها في أقرب وقت ممكن.';
            // Clear form data
            $_POST = [];
        } else {
            throw new Exception('حدث خطأ أثناء إرسال الرسالة');
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get message categories
$categories = $messageManager->getCategories();

// Get user's recent messages
$userMessages = $messageManager->getUserMessages($_SESSION['user_id'], 1, 5);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اسأل معلم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/global-themes.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = '<?php echo SITE_URL; ?>';
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <?php include __DIR__ . '/../includes/header.php'; ?>
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            <div class="page-container">
                <div class="page-header">
                    <h1><i class="fas fa-comments"></i> اسأل معلم</h1>
                    <p>تواصل مع المعلمين واحصل على المساعدة التي تحتاجها</p>
                </div>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <!-- Message Form -->
                <div class="message-form-container">
                    <div class="form-header">
                        <h2><i class="fas fa-paper-plane"></i> إرسال رسالة جديدة</h2>
                        <p>اكتب سؤالك أو استفسارك وسيقوم المعلمون بالرد عليك في أقرب وقت</p>
                    </div>

                    <form method="POST" class="message-form" id="messageForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="subject">
                                    <i class="fas fa-heading"></i> موضوع الرسالة *
                                </label>
                                <input type="text"
                                       id="subject"
                                       name="subject"
                                       class="form-control"
                                       placeholder="اكتب موضوع رسالتك هنا..."
                                       value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                                       maxlength="255"
                                       required>
                                <small class="char-counter">
                                    <span id="subject-count">0</span>/255 حرف
                                </small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group half">
                                <label for="category_id">
                                    <i class="fas fa-tags"></i> التصنيف
                                </label>
                                <select id="category_id" name="category_id" class="form-control">
                                    <option value="">اختر التصنيف</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group half">
                                <label for="priority">
                                    <i class="fas fa-exclamation-triangle"></i> الأولوية
                                </label>
                                <select id="priority" name="priority" class="form-control">
                                    <option value="low" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'low') ? 'selected' : ''; ?>>
                                        منخفضة
                                    </option>
                                    <option value="medium" <?php echo (!isset($_POST['priority']) || $_POST['priority'] == 'medium') ? 'selected' : ''; ?>>
                                        متوسطة
                                    </option>
                                    <option value="high" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'high') ? 'selected' : ''; ?>>
                                        عالية
                                    </option>
                                    <option value="urgent" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'urgent') ? 'selected' : ''; ?>>
                                        عاجلة
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="message_type">
                                <i class="fas fa-list"></i> نوع الرسالة
                            </label>
                            <select id="message_type" name="message_type" class="form-control">
                                <option value="question" <?php echo (!isset($_POST['message_type']) || $_POST['message_type'] == 'question') ? 'selected' : ''; ?>>
                                    سؤال
                                </option>
                                <option value="help" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] == 'help') ? 'selected' : ''; ?>>
                                    طلب مساعدة
                                </option>
                                <option value="complaint" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] == 'complaint') ? 'selected' : ''; ?>>
                                    شكوى
                                </option>
                                <option value="suggestion" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] == 'suggestion') ? 'selected' : ''; ?>>
                                    اقتراح
                                </option>
                                <option value="other" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] == 'other') ? 'selected' : ''; ?>>
                                    أخرى
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">
                                <i class="fas fa-comment-alt"></i> نص الرسالة *
                            </label>
                            <textarea id="message"
                                      name="message"
                                      class="form-control"
                                      rows="8"
                                      placeholder="اكتب رسالتك بالتفصيل هنا..."
                                      maxlength="5000"
                                      required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <small class="char-counter">
                                <span id="message-count">0</span>/5000 حرف
                            </small>
                        </div>

                        <div class="form-actions">
                            <button type="submit" name="send_message" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                إرسال الرسالة
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Recent Messages Section -->
                <?php if (!empty($userMessages['messages'])): ?>
                <div class="recent-messages-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> رسائلك الحديثة</h2>
                        <p>آخر الرسائل التي أرسلتها والردود عليها</p>
                    </div>

                    <div class="messages-grid">
                        <?php foreach ($userMessages['messages'] as $msg): ?>
                            <div class="message-card">
                                <div class="message-header">
                                    <div class="message-subject">
                                        <i class="fas fa-envelope"></i>
                                        <?php echo htmlspecialchars($msg['subject']); ?>
                                    </div>
                                    <div class="message-status status-<?php echo $msg['status']; ?>">
                                        <?php
                                        $statusLabels = [
                                            'pending' => 'في الانتظار',
                                            'read' => 'تم القراءة',
                                            'replied' => 'تم الرد',
                                            'closed' => 'مغلقة'
                                        ];
                                        echo $statusLabels[$msg['status']] ?? $msg['status'];
                                        ?>
                                    </div>
                                </div>

                                <div class="message-meta">
                                    <span class="message-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('Y/m/d H:i', strtotime($msg['created_at'])); ?>
                                    </span>
                                    <span class="message-priority priority-<?php echo $msg['priority']; ?>">
                                        <i class="fas fa-flag"></i>
                                        <?php
                                        $priorityLabels = [
                                            'low' => 'منخفضة',
                                            'medium' => 'متوسطة',
                                            'high' => 'عالية',
                                            'urgent' => 'عاجلة'
                                        ];
                                        echo $priorityLabels[$msg['priority']] ?? $msg['priority'];
                                        ?>
                                    </span>
                                    <?php if ($msg['reply_count'] > 0): ?>
                                        <span class="reply-count">
                                            <i class="fas fa-reply"></i>
                                            <?php echo $msg['reply_count']; ?> رد
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="message-preview">
                                    <?php echo htmlspecialchars(mb_substr($msg['message'], 0, 150)) . (mb_strlen($msg['message']) > 150 ? '...' : ''); ?>
                                </div>

                                <div class="message-actions">
                                    <a href="<?php echo SITE_URL; ?>/page/my_messages.php?id=<?php echo $msg['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="view-all-messages">
                        <a href="<?php echo SITE_URL; ?>/page/my_messages.php" class="btn btn-outline">
                            <i class="fas fa-list"></i>
                            عرض جميع رسائلي
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Help Section -->
                <div class="help-section">
                    <div class="section-header">
                        <h2><i class="fas fa-question-circle"></i> كيفية استخدام النظام</h2>
                        <p>دليل سريع لاستخدام نظام الرسائل بفعالية</p>
                    </div>

                    <div class="help-grid">
                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h3>اكتب رسالتك</h3>
                            <p>اكتب موضوع واضح ومحدد، واشرح سؤالك أو مشكلتك بالتفصيل</p>
                        </div>

                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <h3>اختر التصنيف المناسب</h3>
                            <p>حدد تصنيف رسالتك لتسهيل وصولها للمعلم المختص</p>
                        </div>

                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3>انتظر الرد</h3>
                            <p>سيتم الرد على رسالتك خلال 24 ساعة كحد أقصى</p>
                        </div>

                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h3>تلقى الإشعارات</h3>
                            <p>ستحصل على إشعار فوري عند وصول رد من المعلم</p>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="contact-info-section">
                    <div class="contact-note">
                        <div class="note-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="note-content">
                            <h4>للحالات العاجلة</h4>
                            <p>في حالة الحاجة لمساعدة عاجلة، يمكنك التواصل معنا مباشرة عبر الواتساب:
                               <a href="https://wa.me/+201234567890" target="_blank" class="whatsapp-link">
                                   <i class="fab fa-whatsapp"></i> +20 123 456 7890
                               </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            animation: fadeInUp 0.6s ease-out;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border-radius: 20px;
            color: white;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.3);
        }

        .page-header h1 {
            margin-bottom: 15px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .page-header p {
            font-size: 18px;
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .alert-error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        /* Message Form Styles */
        .message-form-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .form-header h2 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .form-header p {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }

        .message-form {
            text-align: right;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group.half {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .form-group label i {
            margin-left: 8px;
            color: #4682B4;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
            background: #fafbfc;
        }

        .form-control:focus {
            outline: none;
            border-color: #4682B4;
            background: white;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
            transform: translateY(-2px);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 150px;
            line-height: 1.6;
        }

        .char-counter {
            display: block;
            text-align: left;
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #f8f9fa;
        }

        /* Button Styles */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            text-align: center;
            justify-content: center;
        }

        .btn i {
            font-size: 18px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            color: white;
            border: 2px solid transparent;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(70, 130, 180, 0.3);
            background: linear-gradient(135deg, #3a6d96 0%, #7bb8d9 100%);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: 2px solid transparent;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #4682B4;
            border: 2px solid #4682B4;
        }

        .btn-outline:hover {
            background: #4682B4;
            color: white;
            transform: translateY(-2px);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 14px;
        }

        /* Recent Messages Section */
        .recent-messages-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.1);
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .section-header h2 {
            font-size: 2em;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .section-header p {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }

        .messages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .message-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .message-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.15);
            border-color: #4682B4;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .message-subject {
            font-weight: 700;
            color: #333;
            font-size: 16px;
            flex: 1;
            margin-left: 15px;
        }

        .message-subject i {
            margin-left: 8px;
            color: #4682B4;
        }

        .message-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #ffc107; color: #856404; }
        .status-read { background: #17a2b8; color: white; }
        .status-replied { background: #28a745; color: white; }
        .status-closed { background: #6c757d; color: white; }

        .message-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #666;
        }

        .message-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .priority-low { color: #28a745; }
        .priority-medium { color: #ffc107; }
        .priority-high { color: #fd7e14; }
        .priority-urgent { color: #dc3545; }

        .reply-count {
            color: #4682B4;
            font-weight: 600;
        }

        .message-preview {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .message-actions {
            text-align: center;
        }

        .view-all-messages {
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #f8f9fa;
        }

        /* Help Section */
        .help-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.1);
        }

        .help-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .help-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 30px 20px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(70, 130, 180, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .help-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(70, 130, 180, 0.05), transparent);
            transition: left 0.6s;
        }

        .help-card:hover::before {
            left: 100%;
        }

        .help-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.15);
            border-color: rgba(70, 130, 180, 0.3);
        }

        .help-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
        }

        .help-card h3 {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .help-card p {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }

        /* Contact Info Section */
        .contact-info-section {
            background: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.1);
        }

        .contact-note {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            padding: 25px;
            border-radius: 15px;
            color: white;
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .contact-note::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: slide 3s ease-in-out infinite;
        }

        .note-icon {
            font-size: 40px;
            flex-shrink: 0;
        }

        .note-content h4 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
            font-weight: 700;
        }

        .note-content p {
            margin: 0;
            opacity: 0.9;
            line-height: 1.5;
        }

        .whatsapp-link {
            color: #25D366;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .whatsapp-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slide {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: -100%; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .page-container {
                max-width: 95%;
                padding: 15px;
            }
            .messages-grid {
                grid-template-columns: 1fr;
            }
            .help-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .page-container {
                padding: 10px;
            }
            .page-header {
                padding: 20px;
                margin-bottom: 30px;
            }
            .page-header h1 {
                font-size: 2em;
            }
            .page-header p {
                font-size: 16px;
            }
            .message-form-container,
            .recent-messages-section,
            .help-section,
            .contact-info-section {
                padding: 25px 20px;
                margin-bottom: 25px;
            }
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            .form-actions {
                flex-direction: column;
                gap: 10px;
            }
            .btn {
                width: 100%;
                justify-content: center;
            }
            .help-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .contact-note {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }
            .section-header h2 {
                font-size: 1.6em;
            }
            .message-card {
                padding: 20px;
            }
            .message-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
            .message-meta {
                flex-wrap: wrap;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .page-header h1 {
                font-size: 1.8em;
            }
            .page-header p {
                font-size: 14px;
            }
            .message-form-container,
            .recent-messages-section,
            .help-section,
            .contact-info-section {
                padding: 20px 15px;
            }
            .help-card {
                padding: 20px 15px;
            }
            .help-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            .section-header h2 {
                font-size: 1.4em;
            }
            .form-header h2 {
                font-size: 1.6em;
            }
            .btn {
                padding: 15px 20px;
                font-size: 14px;
            }
        }

    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Character counters
            const subjectInput = document.getElementById('subject');
            const messageTextarea = document.getElementById('message');
            const subjectCounter = document.getElementById('subject-count');
            const messageCounter = document.getElementById('message-count');

            if (subjectInput && subjectCounter) {
                function updateSubjectCounter() {
                    const count = subjectInput.value.length;
                    subjectCounter.textContent = count;
                    subjectCounter.style.color = count > 200 ? '#dc3545' : '#666';
                }
                subjectInput.addEventListener('input', updateSubjectCounter);
                updateSubjectCounter();
            }

            if (messageTextarea && messageCounter) {
                function updateMessageCounter() {
                    const count = messageTextarea.value.length;
                    messageCounter.textContent = count;
                    messageCounter.style.color = count > 4500 ? '#dc3545' : '#666';
                }
                messageTextarea.addEventListener('input', updateMessageCounter);
                updateMessageCounter();
            }

            // Form validation
            const messageForm = document.getElementById('messageForm');
            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    const subject = document.getElementById('subject').value.trim();
                    const message = document.getElementById('message').value.trim();

                    if (!subject) {
                        e.preventDefault();
                        alert('يرجى كتابة موضوع الرسالة');
                        document.getElementById('subject').focus();
                        return;
                    }

                    if (!message) {
                        e.preventDefault();
                        alert('يرجى كتابة نص الرسالة');
                        document.getElementById('message').focus();
                        return;
                    }

                    if (subject.length > 255) {
                        e.preventDefault();
                        alert('موضوع الرسالة طويل جداً (الحد الأقصى 255 حرف)');
                        document.getElementById('subject').focus();
                        return;
                    }

                    if (message.length > 5000) {
                        e.preventDefault();
                        alert('نص الرسالة طويل جداً (الحد الأقصى 5000 حرف)');
                        document.getElementById('message').focus();
                        return;
                    }

                    // Show loading state
                    const submitBtn = messageForm.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
                    }
                });
            }

            // Auto-hide alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });

            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all sections
            const sections = document.querySelectorAll('.message-form-container, .recent-messages-section, .help-section, .contact-info-section');
            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });

            // Animate message cards
            const messageCards = document.querySelectorAll('.message-card');
            messageCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = `all 0.4s ease ${index * 0.1}s`;
                observer.observe(card);
            });

            // Animate help cards
            const helpCards = document.querySelectorAll('.help-card');
            helpCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = `all 0.4s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });


    <!-- Global Theme Manager -->
    <script src="<?php echo SITE_URL; ?>/js/global-theme-manager.js"></script>
</body>
</html>
